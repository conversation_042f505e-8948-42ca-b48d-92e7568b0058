apiVersion: apps/v1
kind: Deployment
metadata:
  name:  booking-rule
  namespace: medpro
  labels:
    role:  worker
    app: medpro
spec:
  replicas: 2
  selector:
    matchLabels:
      app: medpro
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        role:  worker
        app: medpro
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                - key: role
                  operator: In
                  values:
                    - "worker"
      serviceAccountName: sva-medpro
      containers:
      - image:  docker.medpro.com.vn/medpro-booking-rules:latest
        name:  medpro-booking-rule
        resources:
          requests:
            cpu: "150m"
            memory: "200M"
          limits:
            cpu: "1000m"
            memory: "2000M"
        readinessProbe:
          httpGet:
            path: /
            port: 6550
          initialDelaySeconds: 30
          timeoutSeconds: 10  
        ports:
        - containerPort:  6550
          name:  port
      restartPolicy: Always
