import { Controller, Get, Post, Request, UseGuards, Body, Inject } from '@nestjs/common';
import { ApiBody, ApiBearerAuth, ApiOperation, ApiTags, ApiExcludeEndpoint } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';

@Controller()
@ApiTags('Admin User - Xác thực người dùng')
export class AppController {
  @Get()
  @ApiExcludeEndpoint()
  async getHello(): Promise<any> {
    return 'Welcome to Medpro Booking Rules Api Gateway';
  }
}
