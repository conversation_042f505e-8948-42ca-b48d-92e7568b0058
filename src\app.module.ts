import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from './config/config.module';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigMongoService } from './config/config.mongo.service';
import { ConfigSentryService } from './config/config.sentry.service';
import { SentryModule } from '@ntegral/nestjs-sentry';
import { BookingRulesModule } from './booking-rules/booking-rules.module';
import { MongoServerSelectionErrorFilter } from './common/filters/mongo-server-selection-error.filter';
import { APP_FILTER } from '@nestjs/core';
import { MongoErrorFilter } from './common/filters/mongo-error.filter';
import { MongooseErrorFilter } from './common/filters/mongoose-error.filter';
import { GlobalSettingModule } from './global-setting/global-setting.module';

@Module({
    imports: [
        MongooseModule.forRootAsync({
            useExisting: ConfigMongoService,
        }),
        ConfigModule,
        // SentryModule.forRootAsync({
        //     useExisting: ConfigSentryService,
        // }),
        BookingRulesModule,
        GlobalSettingModule,
    ],
    controllers: [AppController],
    providers: [
        AppService,
        {
            provide: APP_FILTER,
            useClass: MongoServerSelectionErrorFilter,
        },
        {
            provide: APP_FILTER,
            useClass: MongoErrorFilter,
        },
        {
            provide: APP_FILTER,
            useClass: MongooseErrorFilter,
        },
    ],
})
export class AppModule {}
