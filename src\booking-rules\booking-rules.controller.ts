import { <PERSON>, Post, Headers, Body } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { BookingRulesService } from './booking-rules.service';
import { PatientDetailDto } from './dto/patient-detail-rule.dto';
import { PatientExamRuleDto } from './dto/patient-exam-rule.dto';
import { PatientAgeAcceptRuleDto } from './dto/ patient-age-accept-rule.dto';

@Controller('booking-rules')
@ApiTags('Booking Rules')
export class BookingRulesController {
    constructor(
        private readonly service: BookingRulesService,
    ) { }

    @Post('check-rules')
    async checkRules(
        @Body() formData: any,
        @Headers('locale') locale: string,
    ): Promise<any> {
        return this.service.checkRules(formData, locale);
    }

    @Post('check-rules-multiple')
    async checkRulesMultiple(
        @Body() formData: any,
    ): Promise<any> {
        return this.service.checkRulesMultiple(formData);
    }

    @Post('remove-constraints')
    async removeConstraints(
        @Body() formData: any,
    ): Promise<any> {
        return this.service.removeConstraints(formData);
    }

    @Post('update-success-constraints')
    async updateSuccessConstraints(
        @Body() formData: any,
    ): Promise<any> {
        return this.service.updateSuccessConstraints(formData);
    }

    @Post('cancellation')
    checkCancellationBooking(@Body() booking: any): Promise<any> {
        return this.service.checkCancellationBooking(booking);
    }

    @Post('patient/age-accept')
    checkAgePatientAcceptForPartnerBooking(
        @Headers('partnerid') partnerid: string,
        @Body() formData: PatientAgeAcceptRuleDto,
    ): Promise<any> {
        return this.service.checkAgePatientAcceptForPartnerBooking(formData, partnerid);
    }

    @Post('patient/exam')
    checkPatientRuleExams(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() formData: PatientExamRuleDto,
    ): Promise<any> {
        return this.service.checkPatientRuleExams(formData, partnerid, appid);
    }

    @Post('patient/detail')
    checkConstraintsPatientsRuleDetail(
        @Headers('partnerid') partnerid: string,
        @Headers('locale') locale: string,
        @Body() formData: PatientDetailDto,
    ): Promise<any> {
        return this.service.checkConstraintsPatientsRuleDetail(formData, partnerid, locale);
    }

    @Post('patient/basic-info')
    checkConstraintPatientBasicInfo(
        @Headers('partnerid') partnerid: string,
        @Headers('locale') locale: string,
        @Body() formData: PatientDetailDto,
    ): Promise<any> {
        return this.service.checkConstraintPatientBasicInfo(formData, partnerid, locale);
    }

    // @Post('test-case-umc')
    // async testCaseUmcAgeLimit(@Body() formData: any): Promise<any> {
    //     return this.service.testCheckCaseUmcAge(formData);
    // }

    // @Post('test-case-simmed')
    // async testSimmed(): Promise<any> {
    //     return this.service.testCheckPatientPartnerSimmed();
    // }
}
