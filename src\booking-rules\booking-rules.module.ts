import { ServiceSchema } from '../schemas/service.schema';
import {
    SERVICE_COLLECTION_NAME,
    CITY_COLLECTION_NAME,
    COUNTRY_COLLECTION_NAME,
    DISTRICT_COLLECTION_NAME,
    NATION_COLLECTION_NAME,
    PATIENT_CODE_COLLECTION_NAME,
    WARD_COLLECTION_NAME,
    PATIENT_RELATION_COLLECTION_NAME,
    PROFESSION_COLLECTION_NAME,
    RELATIVE_TYPE_COLLECTION_NAME,
    CONSTRAINTS_BOOKING_RULE_BVMAT_HCM_COLLECTION_NAME,
    CONSTRAINTS_USER_PATIENT_BOOKING_ONEDAY,
} from './../schemas/constants';
import { HttpModule, Module } from '@nestjs/common';
import { BookingRulesService } from './booking-rules.service';
import { BookingRulesController } from './booking-rules.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
    BOOKING_COLLECTION_NAME,
    CONSTRAINTS_BOOKING_RULE_COLLECTION_NAME,
    GLOBAL_SETTING_COLLECTION_NAME,
    HOSPITAL_COLLECTION_NAME,
    PARTNER_CONFIG_COLLECTION_NAME,
    PATIENT_COLLECTION_NAME,
    SUBJECT_COLLECTION_NAME,
} from 'src/schemas/constants';
import { PartnerConfigSchema } from 'src/schemas/partner-config.schema';
import { ConstraintsSchema } from 'src/schemas/constraints.schema';
import { BookingSchema } from 'src/schemas/booking.schema';
import { GlobalSettingSchema } from 'src/schemas/global-setting.schema';
import { PatientSchema } from 'src/schemas/patient.schema';
import { HospitalSchema } from 'src/schemas/hospital.schema';
import { SubjectSchema } from 'src/schemas/subject.schema';
import { CitySchema } from 'src/schemas/city.schema';
import { CountrySchema } from 'src/schemas/country.schema';
import { DistrictSchema } from 'src/schemas/district.schema';
import { NationSchema } from 'src/schemas/nation.schema';
import { PatientCodeSchema } from 'src/schemas/patient-codes.schema';
import { ProfessionSchema } from 'src/schemas/profession.schema';
import { WardSchema } from 'src/schemas/ward.schema';
import { RelativeSchema } from 'src/schemas/relative-mongo.schema';
import { PatientRelationSchema } from '../schemas/patient-relation.schema';
import { ConstraintsBookingBVMatHCMSchema } from 'src/schemas/constraints-booking-bvmathcm.schema';
import { ConstraintsUserPatientBookingOneDaySchema } from '../schemas/constraints-booking-user-patient-one-day.schema';

@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
            { name: CONSTRAINTS_BOOKING_RULE_COLLECTION_NAME, schema: ConstraintsSchema },
            { name: CONSTRAINTS_BOOKING_RULE_BVMAT_HCM_COLLECTION_NAME, schema: ConstraintsBookingBVMatHCMSchema },
            { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
            { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
            { name: SUBJECT_COLLECTION_NAME, schema: SubjectSchema },
            { name: SERVICE_COLLECTION_NAME, schema: ServiceSchema },
            { name: PATIENT_CODE_COLLECTION_NAME, schema: PatientCodeSchema },
            { name: PROFESSION_COLLECTION_NAME, schema: ProfessionSchema },
            { name: NATION_COLLECTION_NAME, schema: NationSchema },
            { name: COUNTRY_COLLECTION_NAME, schema: CountrySchema },
            { name: CITY_COLLECTION_NAME, schema: CitySchema },
            { name: DISTRICT_COLLECTION_NAME, schema: DistrictSchema },
            { name: WARD_COLLECTION_NAME, schema: WardSchema },
            { name: RELATIVE_TYPE_COLLECTION_NAME, schema: RelativeSchema },
            { name: PATIENT_RELATION_COLLECTION_NAME, schema: PatientRelationSchema },
            { name: CONSTRAINTS_USER_PATIENT_BOOKING_ONEDAY, schema: ConstraintsUserPatientBookingOneDaySchema },
        ]),
    ],
    providers: [BookingRulesService],
    controllers: [BookingRulesController],
})
export class BookingRulesModule {}
