import { IService } from 'src/interfaces/service.interface';
import { UtilService } from './../config/util.service';
import { HttpException, HttpService, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
// import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import { Model } from 'mongoose';
import { find, first, get, groupBy, isEmpty, isNil, last, map, size } from 'lodash';
import * as moment from 'moment';
import * as uuid from 'uuid';
import { IPartnerConfig } from 'src/interfaces/partner-config.inteface';
import {
    BOOKING_COLLECTION_NAME,
    CONSTRAINTS_BOOKING_RULE_BVMAT_HCM_COLLECTION_NAME,
    CONSTRAINTS_BOOKING_RULE_COLLECTION_NAME,
    CONSTRAINTS_USER_PATIENT_BOOKING_ONEDAY,
    GLOBAL_SETTING_COLLECTION_NAME,
    HOSPITAL_COLLECTION_NAME,
    PARTNER_CONFIG_COLLECTION_NAME,
    PATIENT_COLLECTION_NAME,
    SUBJECT_COLLECTION_NAME,
} from 'src/schemas/constants';
import {
    PATIENT_CODE_COLLECTION_NAME,
    PATIENT_RELATION_COLLECTION_NAME,
    RELATIVE_TYPE_COLLECTION_NAME,
    SERVICE_COLLECTION_NAME,
} from './../schemas/constants';
import { IConstraintsBookingRule } from 'src/interfaces/constraints.inteface';
import { IBooking } from 'src/interfaces/booking.inteface';
import { IGlobalSetting } from 'src/interfaces/global-setting.interface';
import { IPatient } from 'src/interfaces/patient.inteface';
import { IHospital } from 'src/interfaces/hospital.interface';
import { ISubject } from 'src/interfaces/subject.interface';
import { PatientAgeAcceptRuleDto } from './dto/ patient-age-accept-rule.dto';
import { PatientExamRuleDto } from './dto/patient-exam-rule.dto';
import { PatientDetailDto } from './dto/patient-detail-rule.dto';
import { IPatientCodes } from 'src/interfaces/patient-codes.inteface';
import { IRelative } from 'src/interfaces/relative.inteface';
import { IPatientRelation } from 'src/interfaces/patient-relations.interface';
import { UrlConfigService } from '../config/config.url.service';
import { GlobalSettingService } from '../global-setting/global-setting.service';
import { IConstraintsBVMatHCMBookingRule } from 'src/interfaces/constraints-bvmathcm.inteface';
import { IConstraintsUserPatientBookingOneDay } from '../interfaces/constraints-user-patient-booking-one-day.inteface';

@Injectable()
export class BookingRulesService {
    private logger = new Logger(BookingRulesService.name);
    private readonly AGE_LIMIT_CONFIG: string = 'AGE_LIMIT_CONFIG';

    constructor(
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(CONSTRAINTS_BOOKING_RULE_COLLECTION_NAME) private constraintsBookingRuleModel: Model<IConstraintsBookingRule>,
        @InjectModel(CONSTRAINTS_BOOKING_RULE_BVMAT_HCM_COLLECTION_NAME) private constraintsBVMatHCMModel: Model<IConstraintsBVMatHCMBookingRule>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private readonly hospitalModel: Model<IHospital>,
        @InjectModel(GLOBAL_SETTING_COLLECTION_NAME) private readonly globalSettingModel: Model<IGlobalSetting>,
        @InjectModel(PATIENT_COLLECTION_NAME) private readonly patientModel: Model<IPatient>,
        @InjectModel(SUBJECT_COLLECTION_NAME) private readonly subjectModel: Model<ISubject>,
        @InjectModel(SERVICE_COLLECTION_NAME) private readonly serviceModel: Model<IService>,
        private readonly utilService: UtilService,
        @InjectModel(PATIENT_CODE_COLLECTION_NAME) private patientCodeModel: Model<IPatientCodes>,
        @InjectModel(RELATIVE_TYPE_COLLECTION_NAME) private relativeModel: Model<IRelative>,
        @InjectModel(PATIENT_RELATION_COLLECTION_NAME) private patientRelationModel: Model<IPatientRelation>,
        @InjectModel(CONSTRAINTS_USER_PATIENT_BOOKING_ONEDAY) private constraintUserPatientBookingOneDayModel: Model<IConstraintsUserPatientBookingOneDay>,
        private readonly httpService: HttpService,
        private readonly urlConfigService: UrlConfigService,
        private readonly globalSettingService: GlobalSettingService,
    ) {}

    async processBookingBHYT(formData: any): Promise<any> {
        /* tiến hành tạo constraints */
        if (!formData?.hasInsuranceCode) {
            return true;
        }
        const formatStringDate = moment(formData.startTime)
            .add(7, 'hours')
            .format('DDMMYYYY');
        let constraintsValue = `${formData.partnerId}_${formData.patientId}_${formatStringDate}`;
        if (formData.hasInsuranceCode && !!formData.insuranceCode) {
            constraintsValue = `${constraintsValue}_${formData.insuranceCode}`;
        }
        const objConstraints: any = {
            id: uuid.v4(),
            date: formData.startTime,
            patientId: formData.patientId,
            userId: formData.userId,
            idBooking: formData.bookingUUID,
            status: 0,
            constraintsValue,
            partnerId: formData.partnerId,
        };

        const newConstraint = new this.constraintsBookingRuleModel({
            ...objConstraints,
        });
        try {
            await newConstraint.save();
        } catch (errorDetail) {
            const errorCode = errorDetail.code;
            switch (errorCode) {
                case 11000:
                    /* tìm lại thông tin cái thằng bị trùng */
                    const getDuplicate = await this.constraintsBookingRuleModel
                        .findOne({ constraintsValue }, { idBooking: true, status: true })
                        .exec();
                    const getRepaymentBooking = await this.bookingModel.findOne({ id: getDuplicate.idBooking }, { transactionId: true }).exec();
                    if (getDuplicate.status === 0) {
                        /* tiến hành xóa cái constraintsValue */
                        await this.constraintsBookingRuleModel.findOneAndRemove({ constraintsValue }).exec();
                        await newConstraint.save();
                        break;
                    } else {
                        const messageInform =
                            getDuplicate.status === 0
                                ? 'Bạn đã có một phiếu khám chưa thanh toán trong khung giờ này. Vui lòng thanh toán lại.'
                                : 'Bạn đã đặt khám một lần dùng bảo hiểm y tế trong ngày rồi. Vui lòng xem lại phiếu khám.';
                        throw new HttpException(
                            {
                                errorCode,
                                statusCode: HttpStatus.CONFLICT,
                                message: messageInform,
                                transactionId: getRepaymentBooking?.transactionId || '',
                                bookingStatus: getDuplicate.status,
                            },
                            HttpStatus.CONFLICT,
                        );
                    }
                default:
                    throw new HttpException(
                        {
                            errorCode,
                            statusCode: HttpStatus.BAD_REQUEST,
                            message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.',
                        },
                        HttpStatus.BAD_REQUEST,
                    );
            }
        }
    }

    async checkRules(formData: any, locale: string = 'vi'): Promise<any> {
        const { partnerId, appId, startTime, userId = '', _idPatient = '', cskhInfo = null, 
            serviceId = '', subjectId, patientId, repoName, cskhUserId = null } = formData;

        /* giám sát cò. kiem tra user_patient */
        let isCS = false;
        if (cskhInfo && (cskhInfo?.cskhUserId || '')) {
            isCS = true;
        }

        let service
        if (serviceId) {
            service = await this.serviceModel.findOne({ id: serviceId }).exec();
        }

        //Giới hạn số lượt đặt khám trên 1 hồ sơ đăng ký tại bệnh viện 1 lần / 1 ngày
        if (_idPatient && (partnerId === 'bvmathcm' || partnerId === 'dkdongnai' || (partnerId === 'bvndgiadinh' && service?.serviceType === 'INSURANCE_ONLY'))) {
            console.log('partnerId oneday', partnerId);
            const date = moment(startTime).utc().add(7, 'hours').format('DDMMYYYY');
            const userPatientDate = `${userId}_${_idPatient}_${date}_${partnerId}`
            try {
                const constraintUserPatientOneDay = new this.constraintUserPatientBookingOneDayModel({
                    userId,
                    patientId: _idPatient,
                    date,
                    constraintValue: userPatientDate,
                    partnerId,
                    repoName
                });
                console.log('userPatientDate', userPatientDate);
                await constraintUserPatientOneDay.save();
            } catch (error) {
                console.log('error', error);
                const constraint = await this.constraintUserPatientBookingOneDayModel.findOne({ constraintValue: userPatientDate }).read('primary').exec();
                if (constraint?.status === 1) {
                    if (partnerId === 'bvmathcm') {
                        throw new HttpException('Bệnh viện Mắt TP.HCM chỉ chấp nhận đăng ký trực tuyến 1 lần/24h, nếu bạn có nhu cầu khám chuyên khoa thứ 2 vui lòng đăng ký trực tiếp tại bệnh viện.', HttpStatus.FORBIDDEN)
                    }
                    if (partnerId === 'dkdongnai') {
                        throw new HttpException('Bệnh viện ĐK Đồng Nai chỉ chấp nhận đăng ký trực tuyến 1 lần/24h, nếu bạn có nhu cầu khám chuyên khoa thứ 2 vui lòng đăng ký trực tiếp tại bệnh viện.', HttpStatus.FORBIDDEN)
                    }
                    if (partnerId === 'bvndgiadinh') {
                        throw new HttpException('Bệnh viện Nhân dân Gia Định, đối với khám BHYT, người bệnh chỉ khám chuyên khoa thứ 2 khi có chỉ định của bác sĩ.', HttpStatus.FORBIDDEN)
                    }
                }
            }
        }

        if (partnerId === 'bvndgiadinh' && service?.serviceType === 'INSURANCE_ONLY') {
            const limitCountDays = await this.globalSettingService.findByKeyAndRepoName('LIMIT_COUNT_TIME_BHYT_BOOKING');
            const fiveDaysAgo = moment(startTime).set({ hours: 0, minutes: 0, seconds: 0 }).subtract(+limitCountDays, 'days').toDate();
            const fiveDaysAfter = moment(startTime).set({ hours: 23, minutes: 59, seconds: 59 }).add(+limitCountDays, 'days').toDate();
            const query = {
                date: { $gte: fiveDaysAgo, $lte: fiveDaysAfter },
                status: 1,
                subjectId,
                patientId,
            }
            const checkBookingBefore = await this.bookingModel.find({ ...query }, { date: true, status: true, subjectId: true, patientId: true, serviceInfo: true }).exec();
            if (size(checkBookingBefore) > 0) {
                const bookingBefore = first(checkBookingBefore);
                const bookingDate = moment(bookingBefore.date).format('DD/MM/YYYY');
                const dayValidBefore = moment(bookingBefore.date).subtract((+limitCountDays), 'days').format('DD/MM/YYYY');
                const dayValidAfter = moment(bookingBefore.date).add((+limitCountDays + 1), 'days').format('DD/MM/YYYY');
                throw new HttpException(`Bạn đã có phiếu khám BHYT vào ngày ${bookingDate}, nên chỉ có thể đặt lại lịch khám trước ngày ${dayValidBefore} hoặc kể từ ngày ${dayValidAfter}. Vui lòng chọn lại ngày phù hợp theo quy định.`, HttpStatus.FORBIDDEN)
            }
        }

        const checkChanCoBVMat = await this.globalSettingService.findByKeyAndRepoName('ENV_BOOKING_RULES_CHAN_CO_BVMATHCM');
        // kiểm tra, nếu là CSKH thì ko cần check ràng buộc */
        if (partnerId === 'bvmathcm' && _idPatient && userId && checkChanCoBVMat === 'ON' && isCS === false && !cskhUserId) {

            const cosntraitnDate = moment().utc().add(7, 'hours').format('DDMMYYYY');
            const userDate = `${userId}_${cosntraitnDate}`
            const constraintsBookingBVMat = `${userId}_${_idPatient}_${cosntraitnDate}`
            const objInsert: any = {
                userId,
                patientId: _idPatient,
                date: cosntraitnDate,
                userDate: userDate,
                constraintsValue: constraintsBookingBVMat,
                partnerId: partnerId,
            };

            try {
                /* lưu thông tin constraints cho bv mắt */

                await this.constraintsBVMatHCMModel.create({
                    ...objInsert
                });
            }
            catch (error) {
                console.log(error);
            }
            /* kiem tra lai xem user nay da booking cho patient nao chua. */
            const bookingTimes = await this.constraintsBVMatHCMModel
                .find({
                    userDate
                })
                .read('primary')
                .exec();

            if (bookingTimes.length > 1) {
                console.log(`[BVMAT] Hệ thống chưa xử lý được thao tác này. Vui lòng liên hệ 19002115 để được hỗ trợ.`)
                console.log(JSON.stringify(objInsert, null, 2))
                throw new HttpException('[BVMAT] Hệ thống chưa xử lý được thao tác này. Vui lòng liên hệ 19002115 để được hỗ trợ.', HttpStatus.FORBIDDEN)
            }

        }
        
        /* tìm thông tin cấu hình */
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (partnerConfig) {
            /* tiếp tục kiểm tra rules */
            const getBookingRules = get(partnerConfig, 'bookingRules', []);
            if (getBookingRules.length > 0) {
                /* tiếp tục parser theo , */
                let isError = false;
                let dataError: any = {};
                let status = HttpStatus.BAD_REQUEST;
                for await (const bookingRuleKey of getBookingRules) {
                    const bookingRuleValue = get(partnerConfig, `${bookingRuleKey}`, false);
                    if (bookingRuleValue) {
                        try {
                            switch (bookingRuleKey) {
                                case 'bookingBHYT':
                                    await this.processBookingBHYT(formData);
                                    break;
                                case 'bookingBeforeTest':
                                    await this.bookingBeforeTest(formData);
                                    break;
                                default:
                                    break;
                            }
                        } catch (error) {
                            isError = true;
                            status = error?.response?.statusCode || HttpStatus.BAD_REQUEST;
                            dataError = {
                                errorCode: error?.response?.errorCode || 0,
                                statusCode: status,
                                message: error?.response?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.',
                                transactionId: error?.response?.transactionId || '',
                                bookingStatus: error?.response?.bookingStatus || 0,
                            };
                            this.logger.error(`Error: ${JSON.stringify(dataError, null, 2)}`);
                        }
                        if (isError) {
                            break;
                        }
                    }
                }
                if (isError && Object.keys(dataError).length > 0) {
                    throw new HttpException(dataError, status);
                }
            }

            // rule tuổi
            const partnerHaveAgeConfig = await this.getPartnerConfigAgeLimit(partnerId);
            if (partnerHaveAgeConfig && !(partnerId === 'nhidong1' && formData.treeId === 'ULTRASOUND') && !(partnerId === 'nhidong1' && formData.treeId === 'VACCINE')) {
                let isError = false;
                let dataError: any = {};
                let status = HttpStatus.BAD_REQUEST;
                try {
                    const hospital = await this.hospitalModel.findOne({ partnerId }).exec();
                    let hospitalName: string = '';
                    if (hospital?.name) {
                        hospitalName = hospital.name;
                    }

                    const checkAge = await this.checkOldAccepted(formData.patientId, partnerConfig.patientYearOldAccepted, startTime);
                    if (!checkAge) {
                        throw new HttpException(
                            `Hồ sơ này đã quá tuổi đặt khám tại ${hospitalName}, vui lòng chọn hồ sơ khác hoặc chỉnh sửa hồ sơ.`,
                            HttpStatus.BAD_REQUEST,
                        );
                    }
                } catch (error) {
                    isError = true;
                    status = error?.response?.statusCode || HttpStatus.BAD_REQUEST;
                    dataError = {
                        errorCode: error?.response?.errorCode || 0,
                        statusCode: status,
                        message: error?.response || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.',
                        transactionId: error?.response?.transactionId || '',
                        bookingStatus: error?.response?.bookingStatus || 0,
                    };
                    this.logger.error(`Error: ${JSON.stringify(dataError, null, 2)}`);
                }
                if (isError && Object.keys(dataError).length > 0) {
                    throw new HttpException(dataError, status);
                }
            }

            const { yearOldAccepted } = partnerConfig.toObject();
            // rule tuổi đặt khám
            if (yearOldAccepted) {
                const { conditon, patientYear, warningMessage } = partnerConfig.yearOldAccepted;
                // const patient = await this.patientModel.findOne({ id: formData.patientId });

                const partner = await this.hospitalModel.findOne({ partnerId }).exec();
                const checkAgePatient = await this.checkOldAccepted(formData.patientId, patientYear, startTime);
                // const checkAgePatient =
                //     conditon === 'lt'
                //         ? Number(moment().format('YYYY')) - patient.birthyear < +patientYear // < tuổi qui định
                //         : Number(moment().format('YYYY')) - patient.birthyear > +patientYear; // > tuổi qui định
                const handleMessageWarning =
                    warningMessage.replace('{PARTNER}', partner.name).replace('{CONDITION}', conditon === 'lt' ? 'không đủ tuổi' : 'đã quá tuổi') ||
                    '';
                if (checkAgePatient) {
                    throw new HttpException(handleMessageWarning, HttpStatus.BAD_REQUEST);
                }
            }

            // check rule dat kham cho cac benh vien dc config
            await this.checkPatientPartnerBooking(formData);

            const [envCheckConstraintPatinet, appAcceptConstraint] = await Promise.all([
                this.globalSettingModel.findOne({ key: 'ENV_CHECK_CONSTRAINS_PATIENT_DETAIL' }).exec(),
                this.globalSettingModel.findOne({ key: 'APP_CHECK_CONSTRAINT_PATIENT' }).exec(),
            ]);

            const setAppAcceptConstraint = new Set(appAcceptConstraint.value.split(','));

            if (envCheckConstraintPatinet.value === 'ON' && setAppAcceptConstraint.has(appId)) {
                await this.checkConstrainsPatientDetail(formData.patientId);
            }
        }

        return true;
    }

    checkBirthDate(birthdate: string, ageLimit: number, bookingDate?: string): boolean {
        return Math.floor(moment(bookingDate).diff(moment(new Date(birthdate)), 'years', true)) < +ageLimit;
    }

    checkBirthYear(birthyear: number, ageLimit: number, bookingDate?: string): boolean {
        return Number(moment(bookingDate).format('YYYY')) - birthyear < +ageLimit;
    }

    async checkOldAccepted(patientId: string, ageLimit: number = null, bookingDate?: string): Promise<boolean> {
        const patient = await this.getPatient(patientId);
        // get config
        if (!ageLimit) {
            return true;
        }
        const isValidBirthDate = moment(new Date(patient?.birthdate)).isValid()
            ? this.checkBirthDate(patient.birthdate, +ageLimit, bookingDate)
            : this.checkBirthYear(patient?.birthyear, +ageLimit, bookingDate);
        return isValidBirthDate;
    }

    async removeConstraints(formData: any): Promise<any> {
        return this.constraintsBookingRuleModel
            .remove({
                idBooking: formData.idBooking,
            })
            .exec();
    }

    async updateSuccessConstraints(formData: any): Promise<any> {
        return this.constraintsBookingRuleModel
            .updateMany(
                {
                    idBooking: formData.idBooking,
                    status: 0,
                },
                {
                    status: 1,
                },
            )
            .exec();
    }

    async handleDateForQueryBooking(date: string): Promise<any> {
        const current = moment(date, 'YYYY-MM-DD').add(7, 'hours');
        const fromDate = current.clone().set({
            hours: 0,
            minutes: 0,
            seconds: 0,
        });

        const toDate = current
            .clone()
            .set({
                hours: 23,
                minutes: 59,
                seconds: 59,
            })
            .toDate();

        return { fromDate, toDate };
    }

    async bookingBeforeTest(formData: any): Promise<any> {
        const { partnerId, treeId, patientId, startTime } = formData;
        let dataError: any = {};
        let status = HttpStatus.BAD_REQUEST;
        try {
            if (treeId === 'COVID') {
                // declare date for query booking
                const { fromDate, toDate } = await this.handleDateForQueryBooking(startTime);
                const params = { partnerId, patientId, status: { $in: [1, 2] }, date: { $gte: fromDate, $lt: toDate } };
                // get booking
                const booking = await this.bookingModel.findOne({ ...params }).exec();
                // validate
                if (!booking) {
                    throw new HttpException({ message: `Vui lòng đặt khám trước khi đăng ký xét nghiệm` }, HttpStatus.BAD_REQUEST);
                }
            }
        } catch (error) {
            this.logger.error(`Error when exec bookingBeforeTest() with patientId: ${patientId}\nError: ${error.response.message}`);
            status = error?.response?.statusCode || HttpStatus.BAD_REQUEST;
            dataError = {
                errorCode: error?.response?.errorCode || 0,
                statusCode: status,
                message: error?.response?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.',
                transactionId: error?.response?.transactionId || '',
                bookingStatus: error?.response?.bookingStatus || 0,
            };
            throw new HttpException(dataError, status);
        }
    }

    async getPartnerConfigAgeLimit(partnerId: string): Promise<any> {
        try {
            const ageLimitConfig = await this.globalSettingModel.findOne({ key: this.AGE_LIMIT_CONFIG }).exec();
            const partnerList = ageLimitConfig.value.split(',');
            const partnerExists = partnerList.find(item => item === partnerId);
            if (!partnerExists) {
                return '';
            }
            return true;
        } catch (error) {
            this.logger.error(`Error when exec getPartnerConfigAgeLimit for partnerId: ${partnerId}. Error: ${error.message}`);
            throw error;
        }
    }

    async getPatient(patientId: string): Promise<IPatient> {
        try {
            return this.patientModel.findOne({ id: patientId }).exec();
        } catch (error) {
            this.logger.error(`Error when exec getPatient for patientId: ${patientId}. Error: ${error.message}`);
            throw error;
        }
    }

    async checkRulesMultiple(formDatas: any[]): Promise<any> {
        try {
            for (const formData of formDatas) {
                await this.checkRules(formData);
            }
        } catch (error) {
            throw error;
        }
    }

    async findByKeyAndRepoName(key: string, repoName?: string): Promise<string> {
        let params: any = { key };
        if (repoName) {
            params = { ...params, repoName };
        }

        try {
            const config = await this.globalSettingModel.findOne({ ...params }).exec();
            if (!config) {
                return '';
            }

            return config.value;
        } catch (error) {
            this.logger.error(`Error when findByKey() with repoName: ${key}. Error: ${error.message}`);
            throw error;
        }
    }

    async checkConstrainsPatientDetail(patientId: string): Promise<void> {
        console.log('patientId', patientId);
        const patient = await this.patientModel.findOne({ id: patientId });
        // if (!patient?.country_code.includes('VIE')) {
        if ('5ecb3b014ae1165edc747c5b' !== `${patient?.country || '5ecb3b014ae1165edc747c5b'}`) {
            return;
        }

        console.log('patient', patient);
        const constraintPatientDetailStr = await this.findByKeyAndRepoName('CONSTRAINS_PATIENT_DETAIL_BOOKING_RULE');
        const constraintPatientDetails = JSON.parse(constraintPatientDetailStr);
        console.log('constraintPatientDetails', constraintPatientDetails);
        const groupConstraint = groupBy(constraintPatientDetails, 'key');
        if (constraintPatientDetails && constraintPatientDetails.length > 0) {
            const constraintPatientDetailObj = this.utilService.tranformArrToObj(constraintPatientDetails);
            const keys = Object.keys(constraintPatientDetailObj);
            for (const key of keys) {
                const checkConstraint = get(patient, key);
                console.log('checkConstraint', checkConstraint);
                if (isNil(checkConstraint) || isEmpty(`${checkConstraint}`)) {
                    const constraint = first(groupConstraint[key]);
                    console.log('message', constraint?.message || 'Kiểm tra thông tin hồ sơ bị lỗi !');
                    throw new HttpException(constraint?.message || 'Kiểm tra thông tin hồ sơ bị lỗi !', HttpStatus.BAD_REQUEST);
                }
            }
        }
    }

    checkAgePatient(patient: any, ageLimit?: number, unit: string = 'years', dateCheck?: string): boolean {
        const dateCheckMM = dateCheck && moment(dateCheck).isValid() ? moment(dateCheck) : moment();
        if (!ageLimit) {
            return true;
        }

        if (patient?.birthdate && moment(new Date(patient.birthdate)).isValid()) {
            switch (unit) {
                case 'months':
                    return Math.floor(dateCheckMM.diff(moment(patient?.birthdate, 'YYYY-MM-DD'), 'months', true)) < +ageLimit;
                default:
                    return Math.floor(dateCheckMM.diff(moment(new Date(patient?.birthdate)), 'years', true)) < +ageLimit;
            }
        } else {
            return Number(dateCheckMM.format('YYYY')) - patient?.birthyear < +ageLimit;
        }
    }

    async checkPatientPartner(patients: any, formData: any, partnerId: string): Promise<any> {
        const { subjectId = '', serviceId = '', treeId = '', startTime, bookingDate } = formData;

        const configKeyPartner =
            partnerId === 'umc' ? 'PATIENT_CONSTRAINT_BOOKING_UMC_BETA' : `PATIENT_CONSTRAINT_BOOKING_${partnerId}`.toUpperCase();

        const [config, partner] = await Promise.all([
            this.findByKeyAndRepoName(configKeyPartner),
            this.hospitalModel.findOne({ partnerId }, { name: true }).exec(),
        ]);

        if (!config) {
            return patients;
        }

        const configsObj = JSON.parse(config);

        let constraintConfig: any;
        let subjectOrService = '';
        // ưu tiên dịch vụ nếu đi chung với chuyên khoa. Cấu hình treeId|chuyenkhoa|dichvu
        const keyConstraintConfig = `${treeId.toUpperCase()}|${subjectId}|${serviceId}`;
        constraintConfig = get(configsObj?.constraints, keyConstraintConfig);
        const [service, subject] = await Promise.all([
            this.serviceModel.findOne({ id: serviceId }, { name: true }).exec(),
            this.subjectModel.findOne({ id: subjectId }, { name: true }).exec(),
        ]);
        if (constraintConfig) {
            subjectOrService = (service && `gói dịch vụ ${service?.name}`) || (subject && `chuyên khoa ${subject?.name}`);
        } else {
            const keyConstraintConfig2 = `${treeId.toUpperCase()}|${subjectId || serviceId}`;
            constraintConfig = get(configsObj?.constraints, keyConstraintConfig2) || get(configsObj?.constraints, `${treeId}|${partnerId}_another`);
            subjectOrService = (subject && `chuyên khoa ${subject?.name}`) || (service && `gói dịch vụ ${service?.name}`);
        }

        if (!constraintConfig) {
            return patients;
        }

        const bookingDateCheck =
            bookingDate && moment(bookingDate).isValid()
                ? moment(bookingDate)
                : startTime && moment(startTime).isValid()
                ? moment(startTime)
                : moment();

        const bookingDateCheckString = bookingDateCheck.toISOString();

        patients = await Promise.all(
            patients.map(async patient => {
                let warningMessage: string;
                let messageConfig: string;

                for (const key of Object.keys(constraintConfig)) {
                    switch (key) {
                        case 'age':
                            const minAge = first<number>(constraintConfig[key]?.value);
                            const maxAge = last<number>(constraintConfig[key]?.value);

                            const lessthan = this.checkAgePatient(patient, minAge, constraintConfig[key]?.unit, bookingDateCheckString);
                            const greaterthan = this.checkAgePatient(patient, maxAge, constraintConfig[key]?.unit, bookingDateCheckString);

                            const betweenThan = !lessthan && greaterthan;
                            if (betweenThan) {
                                break;
                            }

                            messageConfig = get(configsObj?.messages, `${constraintConfig[key]?.message}`, '');
                            if (lessthan) {
                                messageConfig = get(configsObj?.messages, `age_lt`, messageConfig);
                            } else if (greaterthan) {
                                messageConfig = get(configsObj?.messages, `age_gt`, messageConfig);
                            }
                            warningMessage = messageConfig.replace('{LABEL}', subjectOrService).replace('{PARTNER}', partner.name);

                            return {
                                ...patient,
                                warningMessage,
                            };
                        default:
                            const prop = get(patient, key);
                            if (isEmpty(`${prop}`) || prop === constraintConfig[key]) {
                                continue;
                            }
                            messageConfig = get(configsObj?.messages, `${key}_${prop}`, '');
                            warningMessage = messageConfig.replace('{LABEL}', subjectOrService).replace('{PARTNER}', partner.name);
                            return {
                                ...patient,
                                warningMessage,
                            };
                    }
                }
                return patient;
            }),
        );

        return patients;
    }

    async checkPatientRuleExams(formdata: PatientExamRuleDto, partnerId: string, appid: string): Promise<any> {
        const [patients, partnerConfig, hospital, warningAddressPatient, ageLimitConfig, warningMessageConfig] = await Promise.all([
            this.patientModel
                .find({ id: { $in: formdata.patientIds } })
                .populate('profession')
                .populate({
                    path: 'country',
                    select: { name: true, _id: true },
                })
                .populate({
                    path: 'nation',
                    select: { name: true },
                })
                .populate({
                    path: 'city',
                    select: { name: true },
                })
                .populate({
                    path: 'district',
                    select: { name: true },
                })
                .populate({
                    path: 'ward',
                    seclect: { name: true },
                })
                .sort({ createdAt: -1 })
                .exec(),
            this.partnerConfigModel.findOne({ partnerId }).exec(),
            this.hospitalModel.findOne({ partnerId }, { name: true }).exec(),
            this.findByKeyAndRepoName('WARNING_ADDRESS_PATIENT'),
            this.findByKeyAndRepoName('AGE_LIMIT_CONFIG'),
            this.findByKeyAndRepoName('WARNING_MESSAGE'),
        ]);
        if (patients.length === 0) {
            return patients;
        }

        const patientObjs = patients.map(patient => patient.toObject());

        let patientsRuleExam = patientObjs;

        // if repo tạo hồ sơ nhanh
        const { appIds: appAllowPatientBasicInfo = [] } = await this.globalSettingService.findByKeyAndRepoNameJSON('CONFIG_PATIENT_BASIC_INFO');

        if (!appAllowPatientBasicInfo.includes(appid)) {
            patientsRuleExam = this.checkPatientAddress(patientObjs, warningAddressPatient);
        }

        if (formdata?.data?.subjectId || formdata?.data?.serviceId) {
            patientsRuleExam = await this.checkPatientPartner(patientsRuleExam, formdata?.data, partnerId);
        }
        patientsRuleExam = this.checkYearOldAccepted(patientsRuleExam, partnerConfig, hospital, formdata?.data?.bookingDate);
        patientsRuleExam = this.checkAgePatientNhidong(
            patientsRuleExam,
            formdata?.data,
            partnerId,
            partnerConfig,
            hospital,
            ageLimitConfig,
            warningMessageConfig,
            'vi',
            formdata?.data?.bookingDate,
        );
        return patientsRuleExam;
    }

    async checkPatientPartnerBooking(formData: any): Promise<void> {
        let dataError: any = {};
        const status = HttpStatus.BAD_REQUEST;
        const patient = await this.patientModel.findOne({ id: formData?.patientId }).exec();
        if (!patient) {
            return;
        }
        const patientObj = patient.toObject();
        const patients = await this.checkPatientPartner([patientObj], formData, formData?.partnerId);
        const firstPatient = first<any>(patients);
        if (!firstPatient?.warningMessage || isEmpty(firstPatient?.warningMessage)) {
            return;
        }
        dataError = {
            errorCode: HttpStatus.BAD_REQUEST,
            statusCode: HttpStatus.BAD_REQUEST,
            message: firstPatient.warningMessage,
            transactionId: formData?.transactionId || '',
            bookingStatus: formData?.bookingStatus || 0,
        };
        throw new HttpException(dataError, status);
    }

    checkYearOldAccepted(patients: any, partnerConfig: IPartnerConfig, hospital: IHospital, bookingDate: string): any {
        // rule tuổi đặt khám
        if (!partnerConfig?.yearOldAccepted?.patientYear) {
            return patients;
        }

        const { conditon, patientYear, warningMessage } = partnerConfig.yearOldAccepted;
        patients = patients.map(patient => {
            const checkAgePatient = this.checkAgePatient(patient, patientYear, 'years', bookingDate);
            const handleMessageWarning =
                warningMessage.replace('{PARTNER}', hospital.name).replace('{CONDITION}', conditon === 'lt' ? 'không đủ tuổi' : 'đã quá tuổi') || '';
            return {
                ...patient,
                ...(checkAgePatient && { warningMessage: handleMessageWarning }),
            };
        });
        return patients;
    }

    // check tuoi theo partner AGE_LIMIT_CONFIG config patientYearOldAccepted (nhidong1 nhidonghcm)
    checkAgePatientNhidong(
        patients: any,
        formData: any,
        partnerId: string,
        partnerConfig: IPartnerConfig,
        hospital: IHospital,
        ageLimitConfig: string,
        warningMessageConfig: string,
        locale: string = 'vi',
        bookingDate: string,
    ): any {
        const partnerList = ageLimitConfig.split(',');
        const partnerExists = partnerList.find(item => item === partnerId);
        if (!partnerExists || (partnerId === 'nhidong1' && formData?.treeId === 'ULTRASOUND') || (partnerId === 'nhidong1' && formData?.treeId === 'VACCINE')) {
            return patients;
        }
        const { patientYearOldAccepted } = partnerConfig;
        const warningMessage = warningMessageConfig.replace('{PARTNER}', hospital.name) || '';
        return patients.map(item => {
            const checkAge = this.checkAgePatient(item, patientYearOldAccepted, 'years', bookingDate);
            return {
                ...item,
                ...(!checkAge && { warningMessage }),
            };
        });
    }

    checkPatientAddress(patients: any, warningAddressPatient: string): any {
        return patients.map(patient => {
            const getCountryObjId = get(patient, 'country._id', '');
            if (`5ecb3b014ae1165edc747c5b` !== `${getCountryObjId}` || patient?.address) {
                return patient;
            }
            return {
                ...patient,
                ...(warningAddressPatient && { warningMessage: warningAddressPatient }),
            };
        });
    }

    async checkConstraintsPatientsRuleDetail(formData: PatientDetailDto, partnerId?: string, locale: string = 'vi'): Promise<any> {
        const constraintPatientGlobalName = `CONSTRAINS_PATIENT_DETAIL${partnerId ? `_${partnerId}`.toUpperCase() : ''}`;
        const [
            colorConstraint,
            patientReltaionDefault,
            ignorePatientWithoutVie,
            partnerCheckRelative,
            constraintPatientDetailGlobal,
            partnerConfig,
            anotherRelationJson,
            patientMongos,
        ] = await Promise.all([
            this.findByKeyAndRepoName('CONSTRAINS_COLOR'),
            this.findByKeyAndRepoName('PATIENT_RELATION_DEFAULT'),
            this.findByKeyAndRepoName('IGNORE_PATIENT_WITHOUT_VIE'),
            this.findByKeyAndRepoName('PARTNER_PATIENT_CONSTRAINT_RELATIVE'),
            this.findByKeyAndRepoName(constraintPatientGlobalName),
            this.partnerConfigModel.findOne({ partnerId }, { patientYearOldAccepted: true }).exec(),
            this.findByKeyAndRepoName('PATIENT_RELATION_DEFAULT'),
            this.patientModel
                .find({ id: { $in: formData.patientIds } })
                .populate({
                    path: 'country',
                    select: { name: true, _id: true },
                })
                .populate({
                    path: 'nation',
                    select: { name: true },
                })
                .populate({
                    path: 'city',
                    select: { name: true },
                })
                .populate({
                    path: 'district',
                    select: { name: true },
                })
                .populate({
                    path: 'ward',
                    seclect: { name: true },
                })
                .sort({ createdAt: -1 })
                .exec(),
        ]);

        let patients = await this.getPatientsBussiness(patientMongos, anotherRelationJson, partnerId);

        let constraintPatientDetailStr = '';
        if (!constraintPatientDetailGlobal) {
            constraintPatientDetailStr = await this.findByKeyAndRepoName('CONSTRAINS_PATIENT_DETAIL');
        } else {
            constraintPatientDetailStr = constraintPatientDetailGlobal;
        }

        const constraintPatientDetails2 = JSON.parse(constraintPatientDetailStr);
        const envRequiredCCCDPatientDetail = await this.globalSettingService.findByKeyAndRepoName('ENV_REQUIRED_CCCD_CONSTRAINT_PATIENT_DETAIL_BVMATHCM')
        console.log('envRequiredCCCDPatientDetail', envRequiredCCCDPatientDetail);
        
        const patientReltaionDefaultObj = JSON.parse(patientReltaionDefault);
        const ignorePatientWithoutVieObj = JSON.parse(ignorePatientWithoutVie);

        if (constraintPatientDetails2 && constraintPatientDetails2.length > 0) {
            patients = patients.map(patient => {
                /* kiểm tra tuổi của hồ sơ đối với bệnh viện mắt */
                let constraintPatientDetails = [...constraintPatientDetails2]
                if (partnerId === 'bvmathcm' && envRequiredCCCDPatientDetail === 'ON') {
                    console.log('partnerId', partnerId);
                    console.log('envRequiredCCCDPatientDetail', envRequiredCCCDPatientDetail);
                    const checkAge14 = this.checkAgePatientV2(patient, 14);
                    if (!checkAge14) {
                        constraintPatientDetails = [...constraintPatientDetails2, {
                            id: uuid.v4().replace(/-/g, ''),
                            key: 'cmnd',
                            message: 'Vui lòng bổ sung thông tin căn cước công dân/ passport'
                        }];
                    }
                }
                const groupConstraint = groupBy(constraintPatientDetails, 'key');
                let constraintPatientDetailObj = this.utilService.tranformArrToObj(constraintPatientDetails);
                // xu lý case relative cho nd1
                if (partnerConfig?.patientYearOldAccepted) {
                    const checkAge = this.checkAgePatient(patient, partnerConfig?.patientYearOldAccepted);
                    // trên patientYearOldAccepted thì bỏ qua case valid thân nhân
                    if (new Set(partnerCheckRelative.split(',')).has(partnerId) && checkAge) {
                        patient = {
                            ...patient,
                            ...patient?.relation,
                        };
                    } else {
                        constraintPatientDetailObj = this.utilService.omit(constraintPatientDetailObj, Object.keys(patient?.relation));
                    }
                }

                let constraintInfo = {
                    isValid: true,
                    color: '',
                    errors: [],
                };
                let errors: any = [];
                const keys = Object.keys(constraintPatientDetailObj);
                for (const key of keys) {
                    let checkConstraint = get(patient, key);
                    if (key === 'patientRelation') {
                        if (checkConstraint?.id === patientReltaionDefaultObj?.id) {
                            checkConstraint = null;
                        }
                    }

                    // if (!patient?.country_code.includes('VIE') && key === 'address') {
                    if ('5ecb3b014ae1165edc747c5b' !== `${patient?.country?._id}` && get(ignorePatientWithoutVieObj, key, false) === true) {
                        continue;
                    }

                    if (key === 'birthdate' && !moment(patient?.birthdate).isValid() && patient?.birthyear) {
                        continue;
                    }

                    if (partnerId !== 'choray' && ['profession', 'address'].includes(key)) {
                        continue;
                    }

                    if (isNil(checkConstraint) || isEmpty(`${checkConstraint}`)) {
                        errors = [...errors, ...groupConstraint[key]];
                    }
                }
                if (errors.length > 0) {
                    constraintInfo = {
                        ...constraintInfo,
                        isValid: false,
                        errors,
                        color: colorConstraint,
                    };
                }

                patient = this.utilService.omit(patient, Object.keys(patient?.relation));

                return { id: patient?.id, constraintInfo };
            });
        }

        return patients;
    }

    async checkConstraintPatientBasicInfo(formData: PatientDetailDto, partnerId?: string, locale: string = 'vi'): Promise<any> {
        const [colorConstraint, ignorePatientWithoutVie, anotherRelationJson, patientMongos] = await Promise.all([
            this.findByKeyAndRepoName('CONSTRAINS_COLOR'),
            this.findByKeyAndRepoName('IGNORE_PATIENT_WITHOUT_VIE'),
            this.findByKeyAndRepoName('PATIENT_RELATION_DEFAULT'),
            this.patientModel
                .find({ id: { $in: formData.patientIds } })
                .populate({
                    path: 'country',
                    select: { name: true, _id: true },
                })
                .populate({
                    path: 'nation',
                    select: { name: true },
                })
                .populate({
                    path: 'city',
                    select: { name: true },
                })
                .populate({
                    path: 'district',
                    select: { name: true },
                })
                .populate({
                    path: 'ward',
                    seclect: { name: true },
                })
                .sort({ createdAt: -1 })
                .exec(),
        ]);

        let patients = await this.getPatientsBussiness(patientMongos, anotherRelationJson, partnerId);

        const constraintPatientDetailStr = await this.findByKeyAndRepoName('CONSTRAINS_PATIENT_DETAIL_BASIC_INFO');

        const constraintPatientDetails = JSON.parse(constraintPatientDetailStr);
        const groupConstraint = groupBy(constraintPatientDetails, 'key');
        const ignorePatientWithoutVieObj = JSON.parse(ignorePatientWithoutVie);

        if (constraintPatientDetails && constraintPatientDetails.length > 0) {
            patients = patients.map(patient => {
                const constraintPatientDetailObj = this.utilService.tranformArrToObj(constraintPatientDetails);

                let constraintInfo = {
                    isValid: true,
                    color: '',
                    errors: [],
                };

                let errors: any = [];
                const keys = Object.keys(constraintPatientDetailObj);
                for (const key of keys) {
                    const checkConstraint = get(patient, key);

                    if ('5ecb3b014ae1165edc747c5b' !== `${patient?.country?._id}` && get(ignorePatientWithoutVieObj, key, false) === true) {
                        continue;
                    }

                    if (key === 'birthdate' && !moment(patient?.birthdate).isValid() && patient?.birthyear) {
                        continue;
                    } else if (isNil(checkConstraint) || isEmpty(`${checkConstraint}`)) {
                        errors = [...errors, ...groupConstraint[key]];
                    }
                }

                if (errors.length > 0) {
                    constraintInfo = {
                        ...constraintInfo,
                        isValid: false,
                        errors,
                        color: colorConstraint,
                    };
                }

                console.log('constraintInfo', constraintInfo);

                patient = this.utilService.omit(patient, Object.keys(patient?.relation));

                return { id: patient?.id, constraintInfo };
            });
        }

        return patients;
    }

    async checkCancellationBooking(booking: any): Promise<boolean> {
        const key = `${booking?.partnerId}|${booking?.treeId}`.toLowerCase();
        const config = await this.findByKeyAndRepoName('NO_CANCELLATION_BOOKING');
        let configObj: any;
        try {
            configObj = JSON.parse(config);
        } catch (error) {
            return;
        }
        if (configObj?.env !== 'ON') {
            return;
        }
        const rule = get(configObj?.rule, key, false);
        if (rule === false) {
            return;
        }
        throw new HttpException(configObj?.message, HttpStatus.BAD_REQUEST);
    }

    checkAgePatientAcceptForPartner(patients: any, partnerId: string, configCheckAgePatient: string, hospitalName: string): any {
        let configCheckAgePatientObj: any;
        try {
            configCheckAgePatientObj = JSON.parse(configCheckAgePatient);
        } catch (error) {
            return patients;
        }
        const configAgePartner = get(configCheckAgePatientObj, `${partnerId}`);

        if (!configAgePartner) {
            return patients;
        }

        const rangeAge = configAgePartner?.value || [];

        if (rangeAge.length === 0) {
            return patients;
        }

        const [minAge, maxAge] = rangeAge;

        return patients.map(patient => {
            const lessthan = this.checkAgePatientV2(patient, minAge, configCheckAgePatientObj?.unit);
            const greaterthan = this.checkAgePatientV2(patient, maxAge, configCheckAgePatientObj?.unit);
            const betweenThan = !lessthan && greaterthan;
            if (betweenThan) {
                return patient;
            }
            if (lessthan) {
                return {
                    ...patient,
                    warningMessage: configAgePartner?.ltMessage.replace('{partner}', hospitalName),
                };
            } else if (!greaterthan) {
                return {
                    ...patient,
                    warningMessage: configAgePartner?.gtMessage.replace('{partner}', hospitalName),
                };
            } else {
                return patient;
            }
        });
    }

    async checkAgePatientAcceptForPartnerBooking(formData: PatientAgeAcceptRuleDto, partnerId: string): Promise<any> {
        const [configCheckAgePatient, hospital, patients] = await Promise.all([
            this.findByKeyAndRepoName('PARTNER_ACCEPT_AGE_PATIENT'),
            this.hospitalModel.findOne({ partnerId }, { name: true }).exec(),
            this.patientModel
                .find({ id: { $in: formData.patientIds } })
                .populate('profession')
                .populate({
                    path: 'country',
                    select: { name: true, _id: true },
                })
                .populate({
                    path: 'nation',
                    select: { name: true },
                })
                .populate({
                    path: 'city',
                    select: { name: true },
                })
                .populate({
                    path: 'district',
                    select: { name: true },
                })
                .populate({
                    path: 'ward',
                    seclect: { name: true },
                })
                .sort({ createdAt: -1 })
                .exec(),
        ]);

        if (patients.length === 0) {
            return [];
        }

        return this.checkAgePatientAcceptForPartner(
            patients.map(patient => patient.toObject()),
            partnerId,
            configCheckAgePatient,
            hospital?.name
        );
    }

    checkAgePatientV2(patient: any, ageLimit?: number, unit: string = 'years'): boolean {
        if (!ageLimit) {
            return true;
        }
        if (patient?.birthdate && moment(new Date(patient.birthdate)).isValid()) {
            switch (unit) {
                case 'months':
                    return Math.floor(moment().diff(moment(patient?.birthdate, 'YYYY-MM-DD'), 'months', true)) < +ageLimit;
                default:
                    return Math.floor(moment().diff(moment(new Date(patient?.birthdate)), 'years', true)) < +ageLimit;
            }
        } else {
            return Number(moment().format('YYYY')) - patient?.birthyear < +ageLimit;
        }
    }

    async getPatientsBussiness(patientMongos: IPatient[], anotherRelationJson: string, partnerId: string): Promise<any> {
        /* lấy thông tin relative */
        let listPatients = [];
        for (const patient of patientMongos) {
            const boj = patient.toObject();
            const { relation } = boj;
            if (relation.relative_type_id) {
                const findRelative = await this.relativeModel.findOne({ id: relation.relative_type_id }).exec();
                if (findRelative) {
                    boj.relative = { ...findRelative.toObject() };
                }
            }
            /* lấy thông tin fullAddress */
            boj.fullAddress = this.getFullAddress(patient);
            listPatients = [...listPatients, boj];
        }
        const mapPatients = map(listPatients, 'id');

        const patientCodes = await this.getInsuranCodeByPatientId(mapPatients, partnerId);

        const anotherRelation = JSON.parse(anotherRelationJson);

        return Promise.all(
            listPatients.map(async item => {
                // const json = item;
                const ngaysinh = moment(item.birthdate).isValid() ? moment(item.birthdate, 'YYYY-MM-DD').format('YYYY-MM-DD') : '';
                let patientCode = item.code;
                let insuranceCode = '';
                const findPatientCode = find(patientCodes, { patientId: item.id });
                if (typeof findPatientCode !== typeof undefined) {
                    const getpatientCode = get(findPatientCode, 'patientCode', null);
                    const getInsuranceCode = get(findPatientCode, 'insuranceCode', null);
                    if (getpatientCode) {
                        patientCode = findPatientCode.patientCode;
                    }
                    if (getInsuranceCode) {
                        insuranceCode = findPatientCode.insuranceCode;
                    }
                }

                const patientRelation = await this.patientRelationModel
                    .findOne({
                        patient: item._id,
                    })
                    .populate({
                        path: 'relationType',
                        select: { id: true, name: true, image: true },
                    })
                    .exec();
                const patientRelationObj = patientRelation ? patientRelation.toObject() : null;
                if (patientRelation) {
                    if (patientRelationObj?.relationType) {
                        const {
                            id = anotherRelation?.id,
                            name = anotherRelation?.name,
                            image = anotherRelation?.image,
                        } = patientRelationObj.relationType;
                        item = {
                            ...item,
                            patientRelation: { id, name, image },
                        };
                    } else {
                        item = {
                            ...item,
                            patientRelation: anotherRelation,
                        };
                    }
                } else {
                    item = {
                        ...item,
                        patientRelation: anotherRelation,
                    };
                }

                return {
                    ...item,
                    birthdate: ngaysinh,
                    fullname: `${item.surname} ${item.name}`,
                    patientCode,
                    isUpdateFull: !insuranceCode,
                    insuranceCode,
                };
            }),
        );
    }

    getFullAddress(patient: IPatient): string {
        let fullAddress = '';
        if (!!patient.address) {
            fullAddress = `${patient.address}`.trim();
        }
        const getWardName = get(patient, 'ward.name', '');
        if (getWardName) {
            fullAddress = `${fullAddress}, ${getWardName}`;
        }
        const getDistrict = get(patient, 'district.name', '');
        if (getDistrict) {
            fullAddress = `${fullAddress}, ${getDistrict}`;
        }
        const getCity = get(patient, 'city.name', '');
        if (getCity) {
            fullAddress = `${fullAddress}, ${getCity}`;
        }

        const getCountryObjId = get(patient, 'country._id', '');
        if (`5ecb3b014ae1165edc747c5b` !== `${getCountryObjId}`) {
            fullAddress = '';
        }

        return fullAddress;
    }

    async getRelationPatient(patients: any): Promise<any> {
        const [patientRelations, anotherRelationJson] = await Promise.all([
            this.patientRelationModel
                .find({
                    patient: { $in: [...patients.map(patient => patient._id)] },
                })
                .populate({
                    path: 'relationType',
                    select: { id: true, name: true, image: true },
                })
                .exec(),
            this.findByKeyAndRepoName('PATIENT_RELATION_DEFAULT'),
        ]);
        const groupPatient = groupBy(patientRelations, 'patient');
        const anotherRelation = anotherRelationJson ? JSON.parse(anotherRelationJson) : null;
        return patients.map(patient => {
            const patientRelation = first(groupPatient[patient._id]);
            if (patientRelation) {
                const patientRelationObj = patientRelation.toObject();
                if (patientRelationObj?.relationType) {
                    const {
                        id = anotherRelation?.id,
                        name = anotherRelation?.name,
                        image = anotherRelation?.image,
                    } = patientRelationObj.relationType;
                    return {
                        ...patient,
                        patientRelation: { id, name, image },
                    };
                } else {
                    return {
                        ...patient,
                        patientRelation: anotherRelation,
                    };
                }
            } else {
                return {
                    ...patient,
                    patientRelation: anotherRelation,
                };
            }
        });
    }

    async getInsuranCodeByPatientId(patientIds: any[], partnerId: string, treeId?: string): Promise<any> {
        const patientCodes = await this.patientCodeModel
            .find({
                partnerId,
                patientId: { $in: patientIds },
            })
            .exec();

        if (treeId && treeId === 'CLS') {
            const patientCodeCls = await this.patientCodeModel
                .find({
                    partnerId,
                    patientId: { $in: patientIds },
                })
                .exec();

            return patientCodeCls || patientCodes;
        }

        return patientCodes;
    }
}
