import { ArgumentsHost, BadRequestException, Catch, ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import { mongo } from 'mongoose';

@Catch(mongo.MongoError)
export class MongoErrorFilter implements ExceptionFilter {

    catch(exception: any, host: ArgumentsHost) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        const exceptionObj = JSON.parse(JSON.stringify(exception));

        if (!(exception instanceof HttpException && exception instanceof BadRequestException)) {
            console.error(`${MongoErrorFilter.name}: `, exception);
            console.error(`${MongoErrorFilter.name}: `, JSON.stringify(exceptionObj, null, 2));
        }

        if (['MongoNetworkError', 'MongoServerSelectionError'].includes(exceptionObj.name)) {
            process.exit(1);
        } else {
            if (exception instanceof HttpException) {
                response
                    .status(exception.getStatus())
                    .json(typeof exception.getResponse() === 'string' ? { message: exception.getResponse() } : exception.getResponse());
            } else {
                response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
                    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                    message: 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.',
                });
            }
        }
    }
}
