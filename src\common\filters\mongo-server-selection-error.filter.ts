import { ArgumentsHost, BadRequestException, Catch, ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import * as mongoose from 'mongoose';


@Catch(mongoose.mongo.MongoServerSelectionError)
export class MongoServerSelectionErrorFilter implements ExceptionFilter {
    catch(exception: any, host: ArgumentsHost) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        const request = ctx.getRequest<Request>();
        const exceptionObj = JSON.parse(JSON.stringify(exception));

        if (!(exception instanceof HttpException && exception instanceof BadRequestException)) {
            console.error(`${MongoServerSelectionErrorFilter.name}: `, exception);
            console.error(`${MongoServerSelectionErrorFilter.name}: `, JSON.stringify(exceptionObj, null, 2));
        }

        if (['MongoServerSelectionError'].includes(exceptionObj.name)) {
            process.exit(1);
        } else {
            if (exception instanceof HttpException) {
                response
                    .status(exception.getStatus())
                    .json(typeof exception.getResponse() === 'string' ? { message: exception.getResponse() } : exception.getResponse());
            } else {
                response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
                    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                    message: 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.',
                });
            }
        }
    }
}
