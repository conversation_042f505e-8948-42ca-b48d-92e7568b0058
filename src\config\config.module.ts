import { Module, Global, HttpModule } from '@nestjs/common';
import { ConfigManagerModule } from '@nestjsplus/config';
import { UrlConfigService } from './config.url.service';
import { UtilService } from './util.service';
import { PkhHttpService } from './config.http.service';
import { ConfigMongoService } from './config.mongo.service';
import { PatientConfigService } from './config.patient.service';
import { ConfigSentryService } from './config.sentry.service';

@Global()
@Module({
  imports: [
    ConfigManagerModule.register({
      useEnv: {
        folder: 'config',
      },
      allowExtras: true,
    }),
    HttpModule,
  ],
  providers: [
    UrlConfigService,
    UtilService,
    PkhHttpService,
    ConfigMongoService,
    PatientConfigService,
    ConfigSentryService,
  ],
  exports: [
    UrlConfigService,
    UtilService,
    PkhHttpService,
    ConfigMongoService,
    PatientConfigService,
    ConfigSentryService,
  ],
})
export class ConfigModule { }
