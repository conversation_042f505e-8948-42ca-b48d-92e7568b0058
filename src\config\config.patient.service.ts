import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Joi from '@hapi/joi';
import { KnexOptions } from './pkhConnection/index';

@Injectable()
export class PatientConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            PKH_PATIENT_HOST: {
                validate: Joi.string(),
                required: false,
                default: 'localhost',
            },
            PKH_PATIENT_PORT: {
                validate: Joi.number(),
                required: false,
                default: 5432,
            },
            PKH_PATIENT_USER: {
                validate: Joi.string(),
                required: false,
                default: 'aaa',
            },
            PKH_PATIENT_DATABASE: {
                validate: Joi.string(),
                required: false,
                default: 'bbbb',
            },
        };
    }

    createKnexOptions(): KnexOptions {
        return {
            client: 'mysql',
            debug: true,
            connection: {
                host: this.get<string>('PKH_PATIENT_HOST'),
                user: this.get<string>('PKH_PATIENT_USER'),
                password: this.get<string>('PKH_PATIENT_PASSWORD'),
                database: this.get<string>('PKH_PATIENT_DATABASE'),
                port: this.get<number>('PKH_PATIENT_PORT'),
            },
        };
    }
}
