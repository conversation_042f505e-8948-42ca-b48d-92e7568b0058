import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';
import { SendgridOptions } from './sendgridConnection';

@Injectable()
export class SendgridConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            SENDGRID_API_KEY: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    setApiKey(): SendgridOptions {
        return {
            apiKey: this.get<string>('SENDGRID_API_KEY'),
        };
    }

}
