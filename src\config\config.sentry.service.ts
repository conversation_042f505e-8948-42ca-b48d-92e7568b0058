import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Joi from '@hapi/joi';
// import { SentryOptionsFactory, SentryModuleOptions } from '@ntegral/nestjs-sentry';
// import { LogLevel } from '@sentry/types';

@Injectable()
//  implements SentryOptionsFactory
export class ConfigSentryService extends ConfigManager {
    provideConfigSpec() {
        return {
            SENTRY_DSN: {
                validate: Joi.string(),
                required: true,
            },
            SENTRY_DEBUG: {
                validate: Joi.boolean(),
                required: true,
            },
            SENTRY_ENVIRONMENT: {
                validate: Joi.string(),
                required: true,
            },
            SENTRY_RELEASE: {
                validate: Joi.string(),
                required: true,
            },
            SENTRY_LOG_LEVEL: {
                validate: Joi.number(),
                required: true,
            },
        };
    }

    // getSentryConfig() {
    //     return {
    //         dsn: this.get<string>('SENTRY_DSN'),
    //         debug: this.get<boolean>('SENTRY_DEBUG'),
    //         environment: this.get<string>('SENTRY_ENVIRONMENT'),
    //         release: this.get<string>('SENTRY_RELEASE'),
    //         logLevel: this.get<number>('SENTRY_LOG_LEVEL'),
    //     };
    // }

    // createSentryModuleOptions(): SentryModuleOptions {
    //     const sentryConfig = this.getSentryConfig();
    //     return {
    //         ...sentryConfig,
    //         // dsn: 'https://<EMAIL>/5356902',
    //         // debug: true,
    //         // environment: 'dev',
    //         // release: null,
    //         // logLevel: LogLevel.Debug,
    //     };
    // }
}
