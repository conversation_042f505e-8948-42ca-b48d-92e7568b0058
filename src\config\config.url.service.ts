import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Joi from '@hapi/joi';

@Injectable()
export class UrlConfigService extends ConfigManager {

    provideConfigSpec() {
        return {
            ENVIRONMENT: {
                validate: Joi.string(),
                required: false,
                default: 'DEVELOPMENT',
            },
            BASE_URL: {
                validate: Joi.string(),
                required: true,
            },
            API_DOCS_JSON: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    getEnv(): string {
        return this.get<string>('ENVIRONMENT');
    }

    getBaseUrl(): string {
        return this.get<string>('BASE_URL');
    }

    getSwaggerJsonUrl(): string {
        return this.get<string>('API_DOCS_JSON');
    }

    getPortalProxyUrl(): string {
        return this.get<string>('API_PORTAL_PROXY');
    }

    loginPortalUrl(): string {
        const baseUrl = this.getPortalProxyUrl();
        return `${baseUrl}/user-service/v1/user/login`;
    }

    getInfoPortalUrl(): string {
        const baseUrl = this.getPortalProxyUrl();
        return `${baseUrl}/user-service/v1/user/info`;
    }
}
