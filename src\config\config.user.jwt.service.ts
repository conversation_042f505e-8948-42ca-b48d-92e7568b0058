import { JwtOptionsFactory, JwtModuleOptions } from '@nestjs/jwt';
import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Joi from '@hapi/joi';

@Injectable()
export class JwtUserConfigService extends ConfigManager implements JwtOptionsFactory {

    provideConfigSpec() {
        return {
            USER_JWT_SECRET: {
                validate: Joi.string(),
                required: true,
            },
            USER_JWT_EXPIRES_IN: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    createJwtOptions(): JwtModuleOptions {
        return {
            secret: this.get<string>('USER_JWT_SECRET'),
            signOptions: { expiresIn: this.get<string>('USER_JWT_EXPIRES_IN') },
        };
    }
}
