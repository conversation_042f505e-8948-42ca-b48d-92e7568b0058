import { ApiProperty } from '@nestjs/swagger';

export class MappingPartnerIdDTO {
    @ApiProperty({
        description: 'API URL HOSPITAL',
        required: true,
        type: String,
    })
    readonly apiUrl: string;

    @ApiProperty({
        description: '<PERSON><PERSON><PERSON> cho bệnh viện cũ hay mới.',
        required: true,
        type: Boolean,
    })
    readonly isOld: boolean;

    @ApiProperty({
        description: 'Dành cho bệnh viện cũ hay mới.',
        required: true,
        type: Number,
    })
    readonly hospitalId: number;
}
