import { Module, DynamicModule, Provider, Global } from '@nestjs/common';
import { FirebaseService } from './firebase.service';
import { FIREBASE_SERVICE_ACCOUNT } from './constants';
import {
    FirebaseOptions,
    FireBaseAsyncServiceAccount,
    FirebaseOptionsFactory,
} from './interfaces';
import { createFirebaseProviders } from './firebase.providers';

import { connectionFactory } from './firebase-connection.provider';

@Global()
@Module({
    providers: [FirebaseService, connectionFactory],
    exports: [FirebaseService, connectionFactory],
})
export class FirebaseAdminModule {
    /**
     * Registers a configured NestKnex Module for import into the current module
     */
    public static register(options: FirebaseOptions): DynamicModule {
        return {
            module: FirebaseAdminModule,
            providers: createFirebaseProviders(options),
        };
    }

    /**
     * Registers a configured NestKnex Module for import into the current module
     * using dynamic options (factory, etc)
     */
    public static registerAsync(options: FireBaseAsyncServiceAccount): DynamicModule {
        return {
            module: FirebaseAdminModule,
            providers: [...this.createProviders(options)],
        };
    }

    private static createProviders(options: FireBaseAsyncServiceAccount): Provider[] {
        if (options.useExisting || options.useFactory) {
            return [this.createOptionsProvider(options)];
        }

        return [
            this.createOptionsProvider(options),
            {
                provide: options.useClass,
                useClass: options.useClass,
            },
        ];
    }

    private static createOptionsProvider(options: FireBaseAsyncServiceAccount): Provider {
        if (options.useFactory) {
            return {
                provide: FIREBASE_SERVICE_ACCOUNT,
                useFactory: options.useFactory,
                inject: options.inject || [],
            };
        }

        // For useExisting...
        return {
            provide: FIREBASE_SERVICE_ACCOUNT,
            useFactory: async (optionsFactory: FirebaseOptionsFactory) =>
                await optionsFactory.createServiceAccount(),
            inject: [options.useExisting || options.useClass],
        };
    }
}
