import { Injectable, Inject, Logger } from '@nestjs/common';
import { FIREBASE_SERVICE_ACCOUNT } from './constants';
import { FirebaseOptions } from './interfaces';
import * as admin from 'firebase-admin';
import * as uuid from 'uuid';

interface IFirebaseService {
    createServiceAccount();
}

@Injectable()
export class FirebaseService implements IFirebaseService {
    private readonly logger: Logger;
    // tslint:disable-next-line: variable-name
    private _firebaseConnection: any;
    // tslint:disable-next-line: variable-name
    constructor(@Inject(FIREBASE_SERVICE_ACCOUNT) private _firebaseOptions: FirebaseOptions) {
        this.logger = new Logger('FirebaseService');
        this.logger.log(`Options: ${JSON.stringify(this._firebaseOptions)}`);
    }

    createServiceAccount() {
        if (!this._firebaseConnection) {
            this._firebaseConnection = admin.initializeApp(this._firebaseOptions, uuid.v4());
        }
        return this._firebaseConnection;
    }
}
