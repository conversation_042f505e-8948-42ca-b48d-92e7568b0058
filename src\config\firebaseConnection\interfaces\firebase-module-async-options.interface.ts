/* Dependencies */
import { ModuleMetadata, Type } from '@nestjs/common/interfaces';

/* Interfaces */
import { FirebaseOptions } from './firebase-options.interface';
import { FirebaseOptionsFactory } from './firebase-options-factory.interface';

export interface FireBaseAsyncServiceAccount extends Pick<ModuleMetadata, 'imports'> {
  inject?: any[];
  useExisting?: Type<FirebaseOptionsFactory>;
  useClass?: Type<FirebaseOptionsFactory>;
  useFactory?: (...args: any[]) => Promise<FirebaseOptions> | FirebaseOptions;
}
