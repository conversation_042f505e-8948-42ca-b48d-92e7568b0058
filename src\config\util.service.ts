import { Injectable } from '@nestjs/common';

@Injectable()
export class UtilService {

    // tslint:disable-next-line: no-empty
    constructor() { }

    getRandomInt = (min: number, max: number): number => {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min)) + min;
    }
    randomText(vLength = 16) {
        const alphabet = 'abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789';
        let pass = [];
        const alphaLength = alphabet.length - 1;
        for (let i = 0; i < vLength; i++) {
            const n = this.getRandomInt(0, alphaLength);
            pass = [...pass, alphabet[n]];
        }
        return pass.join('');
    }

    tranformArrToObj(arr: any[]): any {
        let obj: any = {};
        for (const item of arr) {
            obj = {
                ...obj,
                [`${item.key}`]: item.value,
            };
        }
        return obj;
    }

    transform = (obj, predicate) => {
        return Object.keys(obj).reduce((memo, key) => {
            if(predicate(obj[key], key)) {
                memo[key] = obj[key]
            }
            return memo
        }, {})
    }

    omit = (obj, items) => this.transform(obj, (value, key) => !items.includes(key))

    pick = (obj, items) => this.transform(obj, (value, key) => items.includes(key))
}
