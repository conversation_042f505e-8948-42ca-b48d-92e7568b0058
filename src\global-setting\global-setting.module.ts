import { Global, Module } from '@nestjs/common';
import { GlobalSettingService } from './global-setting.service';
import { MongooseModule } from '@nestjs/mongoose';
import { GLOBAL_SETTING_COLLECTION_NAME, GLOBAL_SETTING_LOCALE_COLLECTION_NAME } from '../schemas/constants';
import { GlobalSettingSchema } from '../schemas/global-setting.schema';
import { GlobalSettingLocaleSchema } from '../schemas/global-setting-locale.schema';

@Global()
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
      { name: GLOBAL_SETTING_LOCALE_COLLECTION_NAME, schema: GlobalSettingLocaleSchema },
    ]),
  ],
  providers: [GlobalSettingService],
  exports: [GlobalSettingService],
})
export class GlobalSettingModule {}
