import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { IGlobalSetting } from 'src/interfaces/global-setting.interface';
import { GLOBAL_SETTING_COLLECTION_NAME, GLOBAL_SETTING_LOCALE_COLLECTION_NAME } from '../schemas/constants';
import { InjectModel } from '@nestjs/mongoose';
import { IGlobalSettingLocale } from '../interfaces/global-setting-locale.interface';
import { isEmpty } from 'lodash';

@Injectable()
export class GlobalSettingService {
    constructor(
        @InjectModel(GLOBAL_SETTING_COLLECTION_NAME) private readonly globalSettingModel: Model<IGlobalSetting>,
        @InjectModel(GLOBAL_SETTING_LOCALE_COLLECTION_NAME)
        private readonly globalSettingLocaleModel: Model<IGlobalSettingLocale>,
    ) {}

    async findByKeyAndRepoName(key: string, repoName?: string, locale: string = 'vi'): Promise<string> {
        let params: any = { key };

        if (repoName) {
            params = { ...params, repoName };
        }

        if (!isEmpty(locale) && locale !== 'vi') {
            const configLocales = await this.globalSettingLocaleModel.findOne(params).exec();

            if (!configLocales || configLocales.translate.length === 0) {
                return '';
            }

            const config = configLocales.translate.find(translate => translate.locale === locale);

            return config?.value || '';
        }

        const config = await this.globalSettingModel.findOne(params).exec();

        if (!config) {
            return '';
        }

        return config.value;
    }

    async findByKeyAndRepoNameJSON(key: string, repoName?: string, locale: string = 'vi'): Promise<any> {
        const value = await this.findByKeyAndRepoName(key, repoName, locale);
        return JSON.parse(value);
    }
}
