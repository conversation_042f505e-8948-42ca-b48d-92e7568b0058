import { Document } from 'mongoose';
import { Expose } from 'class-transformer';

export interface IBooking extends Document {
    id?: string;
    readonly bookingId: string;
    bookingCode: string;
    bookingCodeV1: string;
    readonly bookingSlotId?: string;
    bookingSlot?: string;
    sequenceNumber: number;
    readonly date?: Date;
    readonly bookingChangeTime?: Date;
    readonly prevBookingCode?: string;
    readonly nextBookingCode?: string;
    readonly subjectId: string;
    subject?: string;
    readonly roomId: string;
    room?: string;
    readonly doctorId: string;
    doctor?: string;
    serviceId: string;
    service?: string;
    sectionId?: string;
    section?: string;
    readonly insuranceType: string;
    readonly status: number;
    paymentStatus?: number;
    paymentMessage?: string;
    transactionId?: string;
    readonly errorCode: number;
    readonly errorDescription: string;
    readonly paymentId: string;
    readonly patientId: string;
    patientVersionId: string;
    patient: string;
    patientVersion: string;
    readonly changeTo: string;
    partnerId?: string;
    appId?: string;
    partner?: string;
    platform?: string;
    invoiceId?: string;
    invoiceCode?: string;
    userId?: string;
    prevUserId: string;
    insuranceCode?: string;
    insuranceChoice?: string;
    insuranceTransferCode?: string;
    visible: boolean;
    checkInRoom: object;
    syncStatus?: string;
    syncDate?: Date;
    bookingNote?: string;
    noPayment: boolean;
    serviceType: string;
    idReExam: string;
    syncBookingType: number;
    syncBookingIdV1: number;
    syncPatientIdV1: number;
    syncUserIdV1: number;
    patientNameV1: string;
    patientPhoneV1: string;
    patientMSBNV1: string;
    /*cskh */
    cskhUserId: string;
    createdAt?: Date;
}
