import { Document } from 'mongoose';

export interface IPartnerConfig extends Document {
    id?: string;
    partnerId: string;
    prefixValuePatientCode: string;
    notifAppId: string;
    notifApiKey: string;
    notifAppIdVOIP: string;
    notifApiKeyVOIP: string;
    isVerifiedByPhone: boolean;
    isSearchedByInsuranceCode: boolean;
    isSearcheBeforeCreateNew: boolean;
    isSearchedPhoneCMNDBeforeCreateNew: boolean;
    bookingReminder: boolean;
    isSearcheMedpro: boolean;
    beforeTimeCancelBookingAccepted: string;
    syncBooking: boolean;
    syncPatient: boolean;
    maxId: number;
    extra: object;
    isCountSearchConstraints: boolean;
    reserveAndSync: boolean;
    bookingBHYT: boolean;
    bookingBeforeTest: boolean;
    bookingRules: string[];
    firebase: any;
    isReferralCodeValidator: boolean;
    isVatInvoice: boolean;
    reserveMulti: boolean;
    shareToPay: boolean;
    bookingLimit: number;
    quet_bhyt_btn: boolean;
    da_tung_khamt_btn: boolean;
    tao_ho_so_moi_btn: boolean;
    tim_ho_so_mo_rong_btn: boolean;
    isConfirmDialog: boolean;
    ads?: {
        type: string,
        content: string,
        imageUrl: string,
        url: string,
    };
    qrCodeShareToPay: {
        status: boolean;
        methodId: string;
        detail: string;
    };
    bookingTreeRestApi: string;
    /* hỗ trợ cho phần tra cứu hồ sơ chợ rẫy */
    isSearchExamInput: boolean;
    isSaveExamPatient: boolean;
    joinChat: number;
    joinVideo: number;
    displayCodeBooking?: {
        title?: string;
        type?: string;
        value?: string;
        visible?: boolean;
    };
    commitMessageBHYT: string;
    timeAllowMessage: string;
    popUpProfile: boolean;
    patientYearOldAccepted: number;
    warningRegister: string;
    warning_truoc_ngay?: string;
    reExamsGreeting?: string;
    reExamsBenefic?: string;
    notPaymentYetReminder?: number;
    reExamsDownloadIntro?: string;
    reExamsDownloadImageApp?: string;
    cskh?: [{
        icon: string;
        title: string;
        content: string;
        color: string;
        type: number;
        link: string;
    }];
    yearOldAccepted?: {
        conditon: string;
        patientYear: number;
        warningMessage: string;
    };
    displayCodeBookingV2?: [{
        title: string;
        type: string;
        value: string;
        treeId: string;
    }];
    templateSmsMessage?: string;
    BHYTOptions?: [{
        key: string;
        title: string;
        value: number;
    }];
    qrCodeConfig: {
        templateQrCodeContent: string;
        charSeparateQrcodeContent: string;
        defaultEmptyValue: string;
    };
    canSharepayment?: boolean;
    agreement?: string;
    isConfigNews: boolean;
    footerSupport?: {
        phoneSupport: string;
        zaloUrl: string;
        mapUrl: string;
        visible: boolean;
    };
    countdown?: number;
    configTimeQueryExpired?: {
        startTime: number,
        endTime: number,
    };
}
