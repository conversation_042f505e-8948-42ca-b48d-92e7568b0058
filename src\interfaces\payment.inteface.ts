import { Document } from 'mongoose';

export interface IPayment extends Document {
    bookingId: string;
    type: number;
    date: string;
    patientId: string;
    patient: string;
    amount: number;
    subTotal: number;
    totalFee: number;
    medproFee: number;
    transferFee: number;
    gatewayId: string;
    paymentMethod: string;
    paymentMethodDetail: string;
    transactionId: string;
    bookingCode: string;
    feeCode: string;
    orderId: string;
    paymentTime: Date;
    partnerId: string;
    bankInfo?: {
        name: string;
        accountHolder: string;
        accountNumber: string;
        bankBranch: string;
    };
    extraInfo?: {
        transactionIdV1: string;
        methodIdV1: number;
        booking: object;
    };
    message: string;
    userId?: string;
    hospitalFee?: string;
    reference: number;
    noPayment: boolean;
}
