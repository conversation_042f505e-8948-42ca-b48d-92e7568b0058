import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { BOOKING_SLOT_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const BookingSlotSchema = new Schema({
    bookingSlotId: { type: String, unique: true, required: true },
    roomId: { type: String },
    subjectId: { type: String },
    doctorId: { type: String },
    serviceId: { type: String },
    startTimeString: { type: Date, required: true },
    startTime: { type: Date, required: true },
    endTime: { type: Date },
    maxSlot: { type: Number },
    availableSlot: { type: Number },
    partnerId: { type: String },
}, {
    collection: BOOKING_SLOT_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
