import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { BOOKING_COLLECTION_NAME, BOOKING_SLOT_COLLECTION_NAME, DOCTOR_COLLECTION_NAME, HOSPITAL_COLLECTION_NAME, PATIENT_COLLECTION_NAME, PATIENT_VERSION_COLLECTION_NAME, ROOM_COLLECTION_NAME, SECTION_COLLECTION_NAME, SERVICE_COLLECTION_NAME, SUBJECT_COLLECTION_NAME } from './constants';
const Schema = mongoose.Schema;

export const BookingSchema = new Schema({
    id: { type: String },
    bookingId: { type: String, unique: true, required: true },
    bookingCode: { type: String },
    bookingCodeV1: { type: String },
    insuranceCode: { type: String },
    insuranceType: { type: String },
    insuranceChoice: { type: String },
    insuranceTransferCode: { type: String }, // mã chuyển tuyến
    bookingSlotId: { type: String, required: true },
    bookingSlot: { type: Schema.Types.ObjectId, ref: BOOKING_SLOT_COLLECTION_NAME },
    sequenceNumber: { type: Number },
    date: { type: Date },
    subjectId: { type: String },
    subject: { type: Schema.Types.ObjectId, ref: SUBJECT_COLLECTION_NAME },
    roomId: { type: String },
    room: { type: Schema.Types.ObjectId, ref: ROOM_COLLECTION_NAME },
    sectionId: { type: String },
    section: { type: Schema.Types.ObjectId, ref: SECTION_COLLECTION_NAME },
    doctorId: { type: String },
    doctor: { type: Schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME },
    serviceId: { type: String },
    service: { type: Schema.Types.ObjectId, ref: SERVICE_COLLECTION_NAME },
    status: { type: Number },
    paymentStatus: { type: Number },
    paymentMessage: { type: String },
    transactionId: { type: String },
    errorCode: { type: Number },
    errorDescription: { type: String },
    paymentId: { type: String },
    patientId: { type: String },
    patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
    /* patient version */
    patientVersionId: { type: String },
    patientVersion: { type: Schema.Types.ObjectId, ref: PATIENT_VERSION_COLLECTION_NAME },
    /* patient version */
    userId: { type: String },
    prevUserId: { type: String },
    changeTo: { type: String },
    partnerId: { type: String },
    appId: { type: String },
    partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
    platform: { type: String },
    invoiceId: { type: String },
    invoiceCode: { type: String },
    visible: { type: Boolean, default: true }, /* hiển thị phiếu khám hay ko */
    checkInRoom: { type: Object, default: {} },
    syncStatus: { type: String },
    syncDate: { type: Date },
    bookingNote: { type: String, default: '' },
    noPayment: { type: Boolean, default: false },
    serviceType: { type: String, default: '' },
    idReExam: { type: String, default: '' },
    syncBookingType: { type: Number, default: 2 }, // dành cho các booking ở v1
    syncBookingIdV1: { type: Number, default: 0 },
    syncUserIdV1: { type: Number, default: 0 },
    syncPatientIdV1: { type: Number, default: 0 },
    patientNameV1: { type: String },
    patientPhoneV1: { type: String },
    patientMSBNV1: { type: String },
    bookingChangeTime: { type: Date },
    prevBookingCode: { type: String },
    nextBookingCode: { type: String },
    /* cskh */
    cskhUserId: { type: String },
}, {
    collection: BOOKING_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
