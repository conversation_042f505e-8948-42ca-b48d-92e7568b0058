import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { CITY_COLLECTION_NAME, HOSPITAL_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const CitySchema = new Schema({
    name: String,
    parent: String,
    // priority: Number,
    status: Number,
    partnerId: String,
    code: String,
    createTime: Number,
    sourceUpdateTime: Number,
    updateTime: Number,
    hospitalId: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
}, {
    collection: CITY_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
