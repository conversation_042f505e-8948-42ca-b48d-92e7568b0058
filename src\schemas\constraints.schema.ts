import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { CONSTRAINTS_BOOKING_RULE_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const ConstraintsSchema = new Schema({
    id: { type: String },
    date: { type: Date },
    patientId: { type: String },
    userId: { type: String },
    subjectId: { type: String },
    idBooking: { type: String },
    status: { type: Number },
    constraintsValue: { type: String, required: true, unique: true },
    partnerId: { type: String },
}, {
    collection: CONSTRAINTS_BOOKING_RULE_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
