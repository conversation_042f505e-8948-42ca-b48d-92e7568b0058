import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { COUNTRY_COLLECTION_NAME, HOSPITAL_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const CountrySchema = new Schema({
    name: String,
    code: String,
    partnerId: String,
    hospitalId: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
}, {
    collection: COUNTRY_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
