import * as mongoose from 'mongoose';

import { GLOBAL_SETTING_LOCALE_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

const TranslateSchema = new Schema({
  locale: { type: String },
  value: { type: String },
});

export const GlobalSettingLocaleSchema = new Schema(
  {
    repoName: { type: String },
    key: { type: String, required: true, unique: true },
    translate: [TranslateSchema],
    dataType: { type: String, required: true },
    isDeleted: { type: Boolean, default: false },
    description: { type: String, default: '' },
  },
  {
    collection: GLOBAL_SETTING_LOCALE_COLLECTION_NAME,
    timestamps: true,
    versionKey: false,
  },
);
