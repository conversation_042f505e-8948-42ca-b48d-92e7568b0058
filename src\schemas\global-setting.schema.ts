import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { GLOBAL_SETTING_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const GlobalSettingSchema = new Schema(
    {
        repoName: { type: String },
        key: { type: String, required: true },
        value: { type: String, required: true },
        dataType: { type: String, required: true },
    },
    {
        collection: GLOBAL_SETTING_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
).plugin(jsonMongo);
