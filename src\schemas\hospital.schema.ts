import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { HOSPITAL_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

const HospitalSchema = new Schema({
    partnerId: { type: String, index: { unique: true } },
    name: String,
    short_name: String,
    sms_name: { type: String, uppercase: true },
    image: String,
    banner: { type: String, default: '' },
    status: Number,
    city_id: Number,
    address: String,
    base_url: String,
    lat: Number,
    long: Number,
    hotline: String,
    message: { type: String, default: '' },
}, {
    collection: HOSPITAL_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);

export { HospitalSchema };
