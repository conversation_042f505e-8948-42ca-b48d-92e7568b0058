import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { MAIL_RECEIVER_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const MailReceiverSchema = new Schema({
    email: { type: String },
    type: { type: String, default: 'default' },
}, {
    collection: MAIL_RECEIVER_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
