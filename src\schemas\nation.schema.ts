import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { NATION_COLLECTION_NAME, HOSPITAL_COLLECTION_NAME } from './constants';


const Schema = mongoose.Schema;

export const NationSchema = new Schema({
    code: Number,
    name: String,
    note: String,
    partnerId: String,
    hospitalId: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
}, {
    collection: NATION_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
