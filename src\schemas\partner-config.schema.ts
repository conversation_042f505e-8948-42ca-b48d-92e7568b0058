import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { PARTNER_CONFIG_COLLECTION_NAME } from './constants';
import * as uuid from 'uuid';

const id = () => {
    const uuidv4 = uuid.v4();
    const idv4 = uuidv4.replace(/-/g, '');
    return idv4;
};

const Schema = mongoose.Schema;

export const PartnerConfigSchema = new Schema({
    id: { type: String, default: id },
    partnerId: { type: String, unique: true },
    prefixValuePatientCode: { type: String, default: '' },
    notifAppId: { type: String },
    notifApiKey: { type: String },
    notifAppIdVOIP: { type: String, default: '' },
    notifApiKeyVOIP: { type: String, default: '' },
    isVerifiedByPhone: { type: Boolean, default: true },
    isSearchedByInsuranceCode: { type: Boolean, default: false },
    isSearcheBeforeCreateNew: { type: Boolean, default: false },
    isSearchedPhoneCMNDBeforeCreateNew: { type: Boolean, default: false },
    isSearcheMedpro: { type: Boolean, default: false },
    beforeTimeCancelBookingAccepted: { type: String, default: '16:00:00' },
    syncBooking: { type: Boolean, default: false },
    syncPatient: { type: Boolean, default: false },
    bookingReminder: { type: Boolean, default: false },
    maxId: { type: Number, default: 0 },
    extra: { type: Object, default: {} },
    isCountSearchConstraints: { type: Boolean, default: true },
    reserveAndSync: { type: Boolean, default: false },
    bookingBHYT: { type: Boolean, default: false },
    bookingBeforeTest: { type: Boolean, default: false },
    bookingRules: { type: [String], default: [] },
    firebase: { type: String },
    isReferralCodeValidator: { type: Boolean, default: false },
    isVatInvoice: { type: Boolean, default: false },
    reserveMulti: { type: Boolean, default: false },
    bookingLimit: { type: Number, default: 1 },
    shareToPay: { type: Boolean, default: false },
    quet_bhyt_btn: { type: Boolean, default: true },
    da_tung_khamt_btn: { type: Boolean, default: true },
    tao_ho_so_moi_btn: { type: Boolean, default: true },
    tim_ho_so_mo_rong_btn: { type: Boolean, default: true },
    isConfirmDialog: { type: Boolean, default: true },
    ads: {
        type: { type: String },
        content: { type: String },
        imageUrl: { type: String },
        url: { type: String },
    },
    qrCodeShareToPay: {
        status: { type: Boolean, default: false },
        methodId: { type: String, default: 'atm' },
        detail: { type: String, default: 'VIETCOMBANK' },
    },
    bookingTreeRestApi: { type: String },
    /* hỗ trợ cho phần tra cứu hồ sơ chợ rẫy */
    isSearchExamInput: { type: Boolean, default: false },
    isSaveExamPatient: { type: Boolean, default: false },
    joinChat: { type: Number, default: 120 },
    joinVideo: { type: Number, default: 120 },
    displayCodeBooking: {
        title: { type: String },
        type: { type: String },
        value: { type: String },
        visible: { type: Boolean, default: true },
    },
    commitMessageBHYT: { type: String },
    timeAllowMessage: { type: String },
    popUpProfile: { type: Boolean, default: false },
    patientYearOldAccepted: { type: Number },
    warningRegister: { type: String },
    warning_truoc_ngay: { type: String },
    reExamsGreeting: { type: String },
    reExamsBenefic: { type: String },
    notPaymentYetReminder: { type: String },
    reExamRemindSmsMessageTemplate: { type: String },
    downloadAppMessageTemplate: { type: String },
    numberOfDaySendReExamSmsRemindBefore: { type: Number },
    isAllowSendReExamSms: { type: Boolean },
    examResultNotificationMessageTemplate: { type: String },
    examResultSmsMessageTemplate: { type: String },
    examResultViewUrl: { type: String },
    isSendExamResultRequireFilesChange: { type: Boolean, default: true },
    shortLinkBaseUrl: { type: String },
    reExamsDownloadIntro: { type: String },
    reExamsDownloadImageApp: { type: String },
    cskh: [{
        icon: { type: String },
        title: { type: String },
        content: { type: String },
        color: { type: String },
        type: { type: Number },
        link: { type: String },
    }],
    yearOldAccepted: {
        conditon: { type: String },
        patientYear: { type: Number },
        warningMessage: { type: String },
    },
    displayCodeBookingV2: [{
        title: { type: String },
        type: { type: String },
        value: { type: String },
        treeId: { type: String },
    }],
    templateSmsMessage: { type: String },
    BHYTOptions: [{
        key: { type: String, default: id, unique: true },
        title: { type: String, unique: true, required: true },
        value: { type: Number, unique: true, required: true },
    }],
    qrCodeConfig: {
        templateQrCodeContent: { type: String, default: '' },
        charSeparateQrcodeContent: { type: String, default: '|' },
        defaultEmptyValue:  { type: String, default: 'null' },
    },
    canSharepayment: { type: Boolean, default: true },
    agreement: { type: String },
    isConfigNews: { type: Boolean, default: true },
    footerSupport: {
        phoneSupport: String,
        zaloUrl: String,
        mapUrl: String,
        visible: Boolean,
    },
    countdown: { type: Number, default: 60 },
    configTimeQueryExpired: {
        startTime: Number,
        endTime: Number,
    },
}, {
    collection: PARTNER_CONFIG_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
