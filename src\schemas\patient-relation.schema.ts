import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { PATIENT_COLLECTION_NAME, PATIENT_RELATION_COLLECTION_NAME, RELATIVE_TYPE_COLLECTION_NAME } from './constants';
import { USER_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const PatientRelationSchema = new Schema(
    {
        user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        // patientId: { type: String },
        patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
        relationTypeId: { type: String },
        relationType: { type: Schema.Types.ObjectId, ref: RELATIVE_TYPE_COLLECTION_NAME },
    },
    {
        collection: PATIENT_RELATION_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);
