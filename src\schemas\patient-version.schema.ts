import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { PATIENT_VERSION_COLLECTION_NAME } from './constants';
// import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
// // import { PROFESSION_COLLECTION_NAME } from 'src/profession-mongo/schemas/constants';
// import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
// import { NATION_COLLECTION_NAME } from 'src/nation-mongo/schemas/constants';
// import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
// import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
// import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';

const Schema = mongoose.Schema;

const PatientVersionSchema = new Schema({
    id: String,
    surname: String,
    name: String,
    mobile: String,
    birthdate: String,
    birthyear: Number,
    sex: Number,
    cmnd: String,
    email: String,
    code: String,
    patientCode: String,
    profession_id: String,
    // profession: { type: Schema.Types.ObjectId, ref: PROFESSION_COLLECTION_NAME },
    country_code: String,
    country_id: String,
    // country: { type: Schema.Types.ObjectId, ref: COUNTRY_COLLECTION_NAME },
    dantoc_id: String,
    // nation: { type: Schema.Types.ObjectId, ref: NATION_COLLECTION_NAME },
    city_id: String,
    // city: { type: Schema.Types.ObjectId, ref: CITY_COLLECTION_NAME },
    district_id: String,
    // district: { type: Schema.Types.ObjectId, ref: DISTRICT_COLLECTION_NAME },
    ward_id: String,
    // ward: { type: Schema.Types.ObjectId, ref: WARD_COLLECTION_NAME },
    address: String,
    partnerId: { type: String, default: 'medpro' },
    sourceId: String,
    // userId: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    relation: {
        relative_name: { type: String, default: '' },
        relative_mobile: { type: String, default: '' },
        relative_type_id: { type: String, default: '' },
        relative_email: { type: String, default: '' },
    },
    /* phần ref tới id của patients */
    patientId: { type: String },
}, {
    collection: PATIENT_VERSION_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);

PatientVersionSchema.index({ patientId: 1 });

export { PatientVersionSchema };
