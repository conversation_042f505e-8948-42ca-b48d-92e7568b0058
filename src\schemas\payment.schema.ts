import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { PAYMENT_COLLECTION_NAME, PATIENT_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const PaymentSchema = new Schema({
    id: { type: String },
    type: { type: Number, default: 1 }, // 1=bookingFee , 2=hospitalFee
    bookingId: { type: String },
    date: { type: Date, required: true },
    patientId: { type: String },
    patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
    // paymentId: { type: String, required: true },
    amount: { type: Number, required: true },
    subTotal: { type: Number, required: true },
    totalFee: { type: Number, default: 0 },
    medproFee: { type: Number, default: 0 },
    transferFee: { type: Number, default: 0 },
    status: { type: Number, required: true }, // 1: tạo đơn hàng 2: thanh toán thành công 3: thất bại 4: Expired
    paymentMethod: { type: String, required: true },
    paymentMethodDetail: { type: String },
    gatewayId: { type: String },
    transactionId: { type: String, required: true },
    bookingCode: { type: String },
    feeCode: { type: String },
    orderId: { type: String },
    partnerId: { type: String },
    paymentTime: { type: Date },
    message: { type: String },
    userId: { type: Schema.Types.ObjectId }, // dành cho thanh toán viện phí
    bankInfo: {
        name: { type: String },
        accountHolder: { type: String },
        accountNumber: { type: String },
        bankBranch: { type: String },
    },
    // hospitalFee: { type: Schema.Types.ObjectId, ref: HOSPITAL_FEE_COLLECTION_NAME },
    /* thêm để kiểm tra trường hợp nhiều giao dịch thành công */
    reference: { type: Number, default: 1 },
    extraInfo: {
        transactionIdV1: { type: String, default: '' },
        methodIdV1: { type: Number, default: 0 },
        booking: { type: Object, default: {} },
    },
    noPayment: { type: Boolean, default: false },
}, {
    collection: PAYMENT_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
