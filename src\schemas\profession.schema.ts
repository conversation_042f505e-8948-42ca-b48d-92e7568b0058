import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { PROFESSION_COLLECTION_NAME, HOSPITAL_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const ProfessionSchema = new Schema({
    id: String,
    code: Number,
    name: String,
    partnerId: String,
    hospitalId: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
}, {
    collection: PROFESSION_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
