import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { PUSH_DEVICE_COLLECTION_NAME, HOSPITAL_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const PushDeviceSchema = new Schema({
    id: { type: String },
    clientId: { type: String },
    clientToken: { type: String },
    platform: { type: String },
    userOldId: { type: Number },
    userId: { type: String },
    type: { type: String }, // dkkb - video
    partnerId: { type: String },
    appId: { type: String },
    partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
}, {
    collection: PUSH_DEVICE_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
