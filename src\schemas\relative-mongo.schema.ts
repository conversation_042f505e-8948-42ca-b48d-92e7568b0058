import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { RELATIVE_TYPE_COLLECTION_NAME } from './constants';
import * as uuid from 'uuid';

const id = () => {
    const uuidv4 = uuid.v4();
    const idv4 = uuidv4.replace(/-/g, '');
    return idv4;
};

const Schema = mongoose.Schema;
const RelativeSchema = new Schema({
    id: { type: String, default: id },
    code: String,
    name: String,
    status: Number,
    partnerId: String,
    image: String,
}, {
    collection: RELATIVE_TYPE_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);

export { RelativeSchema };
