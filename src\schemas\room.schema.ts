import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { ROOM_COLLECTION_NAME, HOSPITAL_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const RoomSchema = new Schema({
    code: String,
    name: String,
    shortName: String,
    description: String,
    partnerId: String,
    hospitalId: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
    sectionId: { type: String },
}, {
    collection: ROOM_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
