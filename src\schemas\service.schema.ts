import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { SERVICE_COLLECTION_NAME, HOSPITAL_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const ServiceSchema = new Schema({
    id: String,
    code: String,
    name: String,
    shortName: String,
    partnerId: String,
    price: Number,
    hospitalId: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
    serviceType: { type: String }
}, {
    collection: SERVICE_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
