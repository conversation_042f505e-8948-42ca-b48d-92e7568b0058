import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { SUBJECT_COLLECTION_NAME, HOSPITAL_COLLECTION_NAME } from './constants';
const Schema = mongoose.Schema;

export const SubjectSchema = new Schema({
    code: String,
    name: String,
    shortName: String,
    partnerId: String,
    hospitalId: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
}, {
    collection: SUBJECT_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
