import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { BOOKING_COLLECTION_NAME, HOSPITAL_COLLECTION_NAME, DOCTOR_COLLECTION_NAME, USER_DOCTOR_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const UserDoctorSchema = new Schema({
    id: { type: String },
    partnerId: { type: String },
    partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
    userId: { type: Number },
    email: { type: String, unique: true, required: true },
    doctorId: { type: String },
    doctor: { type: Schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME },
}, {
    collection: USER_DOCTOR_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
