import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { USER_COLLECTION_NAME, PATIENT_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const UserSchema = new Schema({
    username: String,
    password: String,
    salt: String,
    email: String,
    phone: String,
    fullname: String,
    patients: [{ type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME }],
    momoId: String,
    medproId: String,
}, {
    collection: USER_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
