import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { WARD_COLLECTION_NAME, HOSPITAL_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const WardSchema = new Schema({
    code: String,
    name: String,
    parent: String,
    status: Number,
    createTime: Number,
    sourceUpdateTime: Number,
    updateTime: Number,
    partnerId: String,
    hospitalId: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
}, {
    collection: WARD_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
